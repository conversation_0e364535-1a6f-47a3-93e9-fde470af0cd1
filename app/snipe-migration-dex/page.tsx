"use client";
import HistoryListPart from "@/components/SnipeMigrationDex/parts/history-list.part";
import SnipeFormPart from "@/components/SnipeMigrationDex/parts/snipe-form.part";
import SnipingListPart from "@/components/SnipeMigrationDex/parts/sniping-list.part";
import React, { useEffect, useMemo, useRef, useState } from "react";

import { useMediaQuery } from "react-responsive";
import useWindowSize from "@/hooks/useWindowSize";

export default function SnipeMigrationDexPage() {
  const [activeTabMobile, setActiveTabMobile] = useState<string>("snipe");

  const dataTableRef = useRef<HTMLDivElement>(null);
  const isMobile = useMediaQuery({ query: "(max-width: 768px)" });
  const { windowHeight } = useWindowSize();

  useEffect(() => {
    (dataTableRef.current as any)?.filter({});
  }, []);

  const TAB_MOBILE = [
    {
      id: "snipe",
      name: "Snipe",
    },
    {
      id: "sniping-list",
      name: "Sniping List",
    },
    {
      id: "history-list",
      name: "History List",
    },
  ];

  const heightContent = useMemo(() => {
    if (!isMobile) {
      return (windowHeight - 52 - 40 - 40 - 8 - 10 - 30 - 40) / 2;
    }
    return windowHeight - 55 - 50 - 47;
  }, [isMobile, windowHeight]);

  const _renderTabMobile = () => {
    switch (activeTabMobile) {
      case "snipe":
        return (
          <div style={{ height: `${heightContent}px` }}>
            <SnipeFormPart />
          </div>
        );
      case "sniping-list":
        return <SnipingListPart heightContent={heightContent} />;
      case "history-list":
        return <HistoryListPart heightContent={heightContent} />;
      default:
        return <SnipingListPart heightContent={heightContent} />;
    }
  };

  if (isMobile) {
    return (
      <div>
        <div className="border-neutral-alpha-50 mb-[10px] flex h-[40px] border-b">
          {TAB_MOBILE.map((item: any, index) => {
            return (
              <div
                onClick={() => setActiveTabMobile(item.id)}
                key={index}
                className={`hover:text-brand-500 active-tab flex w-max cursor-pointer items-center gap-1 px-4 py-3 text-[12px] leading-[16px] hover:font-semibold ${
                  activeTabMobile === item.id
                    ? "text-brand-500 border-brand-500 border-b font-semibold"
                    : "text-neutral-alpha-800 border-0 font-normal"
                }`}
              >
                {item.name}{" "}
              </div>
            );
          })}
        </div>
        {_renderTabMobile()}
      </div>
    );
  }

  return (
    <div className="flex h-[calc(100vh-var(--height-header-desktop))]">
      <div className="w-[calc(100%-var(--width-snipe-form))]">
        <div className="flex flex-col gap-2">
          <SnipingListPart heightContent={heightContent} />
          <HistoryListPart heightContent={heightContent} />
        </div>
      </div>
      <div className="w-[var(--width-snipe-form)] flex-shrink-0">
        <SnipeFormPart />
      </div>
    </div>
  );
}
