import { <PERSON><PERSON>, <PERSON><PERSON> } from "@/layouts/landing";
import React from "react";
import { AppButton } from "@/components";
import { ArrowUpRight, Logo4, LogoMoonbags } from "@/assets/icons";
import { isMobile } from "@/utils/helper";
import Image from "next/image";

export default function AboutUsPage() {
  return (
    <div className="bg-[#08090C]">
      <Header />
      <div className="relative mx-auto h-max w-full max-w-[1440px] overflow-hidden  bg-[url('/images/home-page/bg-mobile-app.png')] bg-cover bg-top bg-no-repeat">
        <div className="tablet:pt-[170px] tablet:pb-[80px] px-4 pb-4 pt-[50px]">
          <h1 className="bg-gradient-text tablet:text-[48px] mx-auto mb-6 max-w-[734px] text-center text-[24px] font-medium leading-[1.2]">
            Making <PERSON><PERSON> the go-to blockchain for traders
          </h1>

          <p className="tablet:body-md-regular-14 body-sm-regular-12 text-white-500 mx-auto max-w-[734px] text-center">
            Building a comperehensive DeFAI data layer that powers an ecosystem
            of AI enabled trading apps and agents while lowering UX barries to
            make trading on SUI safer, more secure and accessible to all
          </p>
        </div>
      </div>

      <div className="tablet:px-[80px] tablet:py-[40px] mx-auto w-full max-w-[1440px] px-4 py-4">
        <h2 className="bg-gradient-text tablet:text-[28px] mx-auto text-center text-[16px] font-medium leading-[1.2]">
          Ecosystem
        </h2>

        <p className="tablet:body-md-regular-14 body-sm-regular-12 text-white-500 my-2 my-4 text-center">
          Raw on-chain data on Sui is collected, labels it into useful
          information like trading data and Smart Money wallets,... with DeFAI
          Data Layer. Through RaidenX infrastructure, it is accessible by
          forming OAuth and Public APIs, enabling next-gen DeFi, AI-powered and
          third party Dapps built on top
        </p>

        <div className="flex justify-center">
          <a href="https://docs.raidenx.io/docs/dca-order" target="_blank">
            <AppButton
              size={isMobile() ? "small" : "large"}
              variant="secondary"
              className="w-max gap-2"
            >
              Learn More <ArrowUpRight className="tablet:w-6 w-4" />
            </AppButton>
          </a>
        </div>

        <div className="tablet:mt-[40px] mt-[24px] flex justify-center">
          <Image
            src={"/images/home-page/ecosystem-mobile.png"}
            alt={"Ecosystem RaidenX"}
            className="tablet:hidden block"
            width={375}
            height={200}
          />
          <Image
            src={"/images/home-page/ecosystem.png"}
            alt={"Ecosystem RaidenX"}
            className="tablet:block hidden"
            width={1280}
            height={392}
          />
        </div>
      </div>

      <div className="tablet:px-[80px] tablet:py-[40px] mx-auto w-full max-w-[1440px] px-4 py-4">
        <h2 className="bg-gradient-text tablet:text-[28px] mx-auto text-center text-[16px] font-medium leading-[1.2] ">
          Products
        </h2>

        <div className="tablet:grid-cols-8 tablet:gap-8 tablet:mt-[40px] mx-auto mt-4 grid grid-cols-1 gap-4">
          <div className="tablet:col-span-4 bg-white-50 border-white-100 rounded-[16px] border">
            <div className="tablet:px-8 tablet:pt-8 px-4 pt-4">
              <Image
                src={"/images/home-page/raidenx-project.png"}
                alt={"RaidenX Project"}
                className="tablet:h-[285px] h-[200px] w-full rounded-t-[12px] object-cover"
                style={{
                  boxShadow: "0px 0px 16px 6px rgba(255, 255, 255, 0.05)",
                }}
                width={632}
                height={285}
              />
            </div>

            <div className="tablet:px-6 tablet:py-4 border-white-100 border-t px-3 py-3">
              <div className="mb-2">
                <Logo4 className="-ml-[12px] h-8 w-[100px]" title="RaidenX" />
              </div>
              <p className="tablet:body-md-regular-14 body-sm-regular-12 text-white-500">
                A cross-platform trading terminal providing essential tools to
                support traders throughout their journey and enhance their
                performance
              </p>
            </div>
          </div>
          <div className="tablet:col-span-4 bg-white-50 border-white-100 rounded-[16px] border">
            <a href="https://moonbags.io" target="_blank">
              <div className="tablet:px-8 tablet:pt-8 px-4 pt-4">
                <Image
                  src={"/images/home-page/moonbag-project.png"}
                  alt={"Moonbags Project"}
                  className="tablet:h-[285px] h-[200px] w-full rounded-t-[12px] object-cover"
                  style={{
                    boxShadow: "0px 0px 16px 6px rgba(255, 255, 255, 0.05)",
                  }}
                  width={632}
                  height={285}
                />
              </div>

              <div className="tablet:px-6 tablet:py-4 border-white-100 border-t px-3 py-3">
                <div className="mb-2">
                  <LogoMoonbags title="Moonbags" />
                </div>
                <p className="tablet:body-md-regular-14 body-sm-regular-12 text-white-500 line-clamp-2">
                  Next gen launchpad to stop memes from becoming a negative-sum
                  game with revenue sharing features
                </p>
              </div>
            </a>
          </div>
        </div>
      </div>
      <Footer isHideDataService />
    </div>
  );
}
