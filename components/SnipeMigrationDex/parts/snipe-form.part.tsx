"use client";
import clsx from "clsx";
import * as React from "react";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useMediaQuery } from "react-responsive";
import { ChevronDownIcon, WalletIcon } from "@/assets/icons";
import { AmountForm } from "@/components/Snipe/amount-form";
import AvatarSelectPair from "@/components/Snipe/AvatarSelectPair";

import { FooterForm } from "@/components/Snipe/footer-form";
import { getTokenNameFromTokenAddress } from "@/components/Snipe/helper";
import { SettingsForm } from "@/components/Snipe/settings-form";
import { IShowModalSnipe, ISnipeForm } from "@/components/Snipe/type";
import { WalletSelection } from "@/components/Snipe/wallet-selection";
import { useSnipeWallet } from "@/hooks/useSnipeWallet";
import { AppBroadcast, BROADCAST_EVENTS } from "@/libs/broadcast";
import { toastError, toastSuccess } from "@/libs/toast";
import { ModalBottomMobile } from "@/modals";
import rf from "@/services/RequestFactory";
import { RootState } from "@/store";
import { setIsShowModalAddWallet } from "@/store/metadata.store";
import { useOrder } from "@/hooks";
import Storage from "@/libs/storage";
import { SUI_ADDRESS } from "../../Snipe/constant";
import { formatNumber } from "@/utils/format";
import { ModalAutoSell } from "@/components/OrderForm/buy-order/AutoSellForm";

type SnipeFormPartProps = {
  onClose?: () => void;
};

const SnipeFormPart = ({ onClose }: SnipeFormPartProps) => {
  const dispatch = useDispatch();
  const { accessToken, network, wallets, platformSnipe } = useSelector(
    (state: RootState) => ({
      accessToken: state.user.accessToken,
      network: state.user.network,
      wallets: state.user.wallets,
      platformSnipe: state.metadata.platformSnipe,
    })
  );
  const isTabletOrMobile = useMediaQuery({ query: "(max-width: 992px)" });

  const [showModal, setShowModal] = useState<IShowModalSnipe>({
    settings: false,
    selectWallet: false,
    modalWalletSelection: false,
    modalSettingsOrder: false,
    options: false,
    targetPair: false,
  });
  const snipeSettings = Storage.getSnipeSettings();
  const [snipeForm, setSnipeForm] = useState<ISnipeForm>({
    buyAmount: "",
    targetTokenAddress: "",
    targetTokenQuoteAddress: SUI_ADDRESS,
    buyByToken: SUI_ADDRESS,
  });

  // auto sell
  const [isSettingAutoSell, setIsSettingAutoSell] = useState<boolean>(
    snipeSettings?.snipeMigrationDex?.autoSell?.isSettingAutoSell || false
  );
  const [isShowSettingAutoSell, setIsShowSettingAutoSell] =
    useState<boolean>(false);
  const [triggersOrder, setTriggersOrder] = useState<any[]>(
    snipeSettings?.snipeMigrationDex?.autoSell?.triggers || [
      {
        priceChangePercent: 10,
        sellPercent: 50,
      },
      {
        priceChangePercent: -10,
        sellPercent: 50,
      },
    ]
  );

  const { settings } = useOrder();

  const { activeSnipeWallets, activeTotalQuoteBalance } = useSnipeWallet(
    snipeForm.buyByToken
  );

  const createOrder = async () => {
    if (!snipeForm.targetTokenAddress) {
      toastError("Error", "Please enter target token address");
      return;
    }

    if (!+snipeForm.buyAmount) {
      toastError("Error", "Please enter amount");
      return;
    }

    try {
      let autoSellSettings = null as any;

      if (isSettingAutoSell) {
        if (!triggersOrder?.length) {
          toastError("Error", "Please setting for auto sell");
          return;
        }

        autoSellSettings = {
          isActive: true,
          receiveToken: null,
          triggers: triggersOrder,
        };
      }

      const res = await rf
        .getRequest("SnipeMigrationDexRequest")
        .createSnipeDex(network, {
          autoSellSettings,
          buyAmount: +snipeForm.buyAmount,
          slippage: +settings.snipeSlippage || 0,
          gasPrice: +settings.snipeGasPrice || 0,
          tipAmount: settings.snipeTipAmount,
          buyByToken: snipeForm.buyByToken,
          targetTokenAddress: snipeForm.targetTokenAddress,
          userWalletAddresses: activeSnipeWallets.map(
            (wallet) => wallet.address
          ),
        });
      const allFailed = res.every((snipe: any) => !snipe.isSuccess);

      if (allFailed) {
        toastError(
          "Error",
          "This snipe listing setting existed, only update or delete"
        );
        return;
      }
      AppBroadcast.dispatch(BROADCAST_EVENTS.REFRESH_DATA_SNIPING_LIST);
      const walletsFailed = res.filter((snipe: any) => !snipe.isSuccess);
      if (walletsFailed.length > 0) {
        toastError(
          "Error",
          `Wallet ${walletsFailed
            .map((wallet: any) => wallet.userWalletAddress)
            .join(", ")} dex listing setting existed, only update or delete`
        );
        return;
      }
      setSnipeForm({
        ...snipeForm,
        targetTokenAddress: "",
        buyAmount: "",
      });
      toastSuccess("Success", "Snipe order created successfully!");
    } catch (error: any) {
      toastError("Error", error.message || "Something went wrong!");
    }
  };
  const onShowSettings = () => {
    if (!accessToken) return;
    if (isTabletOrMobile) {
      setShowModal({
        ...showModal,
        modalSettingsOrder: true,
      });
      return;
    }
    setShowModal({
      ...showModal,
      settings: true,
    });
  };

  const toggleSettingAutoSell = () => {
    if (!accessToken) return;

    if (isSettingAutoSell) {
      setIsSettingAutoSell(false);
      Storage.setSnipeSettings({
        ...snipeSettings,
        snipeMigrationDex: {
          ...snipeSettings?.snipeMigrationDex,
          autoSell: {
            ...snipeSettings?.snipeMigrationDex?.autoSell,
            isSettingAutoSell: false,
          },
        },
      });
      return;
    }

    setIsSettingAutoSell(true);
    setIsShowSettingAutoSell(true);

    Storage.setSnipeSettings({
      ...snipeSettings,
      snipeMigrationDex: {
        ...snipeSettings?.snipeMigrationDex,
        autoSell: {
          ...snipeSettings?.snipeMigrationDex?.autoSell,
          isSettingAutoSell: true,
        },
      },
    });
  };

  const _renderContent = () => {
    if (showModal.settings) {
      return (
        <SettingsForm
          onCloseSettings={() =>
            setShowModal({ ...showModal, settings: false })
          }
        />
      );
    }
    if (showModal.selectWallet) {
      return (
        <WalletSelection
          snipeForm={snipeForm}
          onCloseWalletSettings={() =>
            setShowModal({ ...showModal, selectWallet: false })
          }
        />
      );
    }

    return (
      <>
        <div className="border-white-50 bg-black-900 mb-3 flex h-full max-h-[34px] items-center gap-2 rounded border px-1 pl-1 pr-2">
          <div className="text-white-800 border-white-50 border-r pl-[2px] pr-1 text-[12px] font-normal leading-[18px]">
            Token address
          </div>
          <input
            value={snipeForm.targetTokenAddress}
            onChange={(e) =>
              setSnipeForm({
                ...snipeForm,
                targetTokenAddress: e.target.value,
              })
            }
            type="text"
            className="text-white-1000 placeholder:text-white-200 bg-black-900 flex-1 text-[12px] font-normal leading-[18px] focus:outline-none"
            placeholder="Add token address"
          />
        </div>

        <div>
          {!!wallets.length ? (
            <div
              onClick={() => setShowModal({ ...showModal, selectWallet: true })}
              className="bg-neutral-alpha-50 flex cursor-pointer items-center justify-between rounded-[4px] p-[4px]"
            >
              <div className="flex items-center gap-2">
                <div className="tablet:bg-neutral-beta-500 flex h-6 w-6 items-center justify-center rounded-[4px]">
                  <WalletIcon />
                </div>
                <div className="flex gap-2">
                  <div className="body-sm-semibold-12">Wallet</div>
                  <div className="body-xs-regular-10 text-brand-500 bg-brand-800 border-brand-800 rounded-[80px] border px-[6px]">
                    {activeSnipeWallets.length} selected
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <div className="flex items-center gap-1">
                  <div className="body-sm-regular-12">
                    {formatNumber(activeTotalQuoteBalance, 4)}
                  </div>
                  <AvatarSelectPair
                    coin={getTokenNameFromTokenAddress(snipeForm.buyByToken)}
                  />
                </div>

                <div>
                  <ChevronDownIcon className="text-neutral-alpha-500 h-[16px] w-[16px] rotate-[-90deg]" />
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-neutral-alpha-50 flex items-center justify-between rounded-[4px] p-[4px]">
              <div className="flex items-center gap-2">
                <div className="bg-neutral-beta-500 flex h-6 w-6 items-center justify-center rounded-[4px]">
                  <WalletIcon />
                </div>
                <div className="flex gap-2">
                  <div className="body-sm-semibold-12">Wallet</div>
                </div>
              </div>
              {accessToken && (
                <div
                  className="action-xs-medium-12 text-brand-500 cursor-pointer"
                  onClick={() => {
                    dispatch(setIsShowModalAddWallet({ isShow: true }));
                  }}
                >
                  Add Wallet
                </div>
              )}
            </div>
          )}
        </div>

        <div>
          <AmountForm setSnipeForm={setSnipeForm} snipeForm={snipeForm} />
        </div>

        <FooterForm
          snipeForm={snipeForm}
          createOrder={createOrder}
          balance={activeTotalQuoteBalance}
          onShowSettings={onShowSettings}
          toggleSetAutoSell={toggleSettingAutoSell}
          autoSell={isSettingAutoSell}
          onShowSettingAutoSell={() => setIsShowSettingAutoSell(true)}
        />

        <ModalAutoSell
          isShowReceiveSui={false}
          onClose={() => setIsShowSettingAutoSell(false)}
          isOpen={isShowSettingAutoSell}
          setTriggersOrder={setTriggersOrder}
          triggersOrder={triggersOrder}
          typeSnipe="migrationDex"
        />
      </>
    );
  };

  return (
    <div
      className={clsx(
        "bg-neutral-alpha-50 tablet:pr-[8px] border-neutral-alpha-50 tablet:overflow-hidden flex h-full w-full overflow-auto border-b px-[8px] py-[12px]",
        showModal.selectWallet ? "pr-2" : "pr-2"
      )}
    >
      <div className="w-full">{_renderContent()}</div>

      {showModal.modalWalletSelection && (
        <ModalBottomMobile
          isOpen={showModal.modalWalletSelection}
          onClose={() =>
            setShowModal({ ...showModal, modalWalletSelection: false })
          }
        >
          <div className="w-full p-[16px]">
            <WalletSelection
              snipeForm={snipeForm}
              onCloseWalletSettings={() =>
                setShowModal({ ...showModal, selectWallet: false })
              }
            />
          </div>
        </ModalBottomMobile>
      )}

      {showModal.modalSettingsOrder && (
        <ModalBottomMobile
          isOpen={showModal.modalSettingsOrder}
          onClose={() =>
            setShowModal({ ...showModal, modalSettingsOrder: false })
          }
        >
          <div className="w-full p-[16px]">
            <SettingsForm
              onCloseSettings={() =>
                setShowModal({ ...showModal, settings: false })
              }
            />
          </div>
        </ModalBottomMobile>
      )}
    </div>
  );
};
export default SnipeFormPart;
