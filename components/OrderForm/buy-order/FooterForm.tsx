import {
  ChevronDownIcon,
  CoinTip,
  GasIcon,
  SettingsIcon,
  SlippageIcon,
} from "@/assets/icons";
import * as React from "react";
import { useCallback, useEffect, useRef, useState } from "react";
import BigNumber from "bignumber.js";
import { dividedBN, multipliedBN } from "@/utils/helper";
import { debounce } from "lodash";
import { ButtonSignIn } from "@/components/OrderForm/ButtonSignin";
import { ButtonAddWallet } from "@/components/OrderForm/ButtonAddWallet";
import { ButtonAddFund } from "@/components/OrderForm/ButtonAddFund";
import { getDexToWhenAfterGraduated, isDexHasBondingCurve } from "@/utils/dex";
import { formatNumber } from "@/utils/format";
import { AppButton, AppToggle } from "@/components";
import { useOrder, useUser } from "@/hooks";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { useMediaQuery } from "react-responsive";
import { TPair, TPairPrice } from "@/types";
import { OrderFormType, OrderLimitTargetType } from "@/enums";
import {
  getCirculatingSupply,
  isBuyBySuiToken,
  isPairWithSui,
} from "@/utils/pair";
import { useTradingWallet } from "@/hooks/useTradingWallet";
import { TRADE_TYPE } from "@/enums/trade.enum";
import { usePairPrice } from "@/hooks/usePairPrice";

export const FooterForm = ({
  autoSell,
  toggleSetAutoSell,
  amount,
  pair,
  orderType,
  targetType,
  targetPrice,
  targetMC,
  setPercent,
  onShowSettings,
  createOrder,
  tokenQuoteSelected,
  amountOrders,
  amountTime,
  resolution,
  onShowSettingAutoSell,
}: {
  amountOrders: any;
  amountTime: any;
  resolution: string;
  autoSell: boolean;
  toggleSetAutoSell: () => void;
  onShowSettingAutoSell: () => void;
  amount: any;
  pair: TPair;
  orderType: OrderFormType;
  targetType: OrderLimitTargetType;
  targetPrice: string;
  targetMC: string;
  setPercent: (value: string) => void;
  onShowSettings: () => void;
  createOrder: () => void;
  tokenQuoteSelected: string;
}) => {
  const { pairPrice } = usePairPrice(pair);
  const [tokenAmountEstimate, setTokenAmountEstimate] = useState<any>("");
  const tokenAmountEstimateRef = useRef<any>("");
  const targetConvertPercent = useRef("1");
  const { settings, estimateQuickBuy } = useOrder();
  const { activeWalletAddresses } = useUser();
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const wallets = useSelector((state: RootState) => state.user.wallets);
  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });
  const { activeTotalQuoteBalance } = useTradingWallet(
    pair,
    tokenQuoteSelected
  );

  const isExceedBalance = () => {
    return new BigNumber(amount).isGreaterThan(activeTotalQuoteBalance);
  };

  const debouncedEstimateQuickBuy = useCallback(
    debounce((pair, amount) => {
      estimateQuickBuy(
        pair,
        amount,
        !isPairWithSui(pair) && isBuyBySuiToken(tokenQuoteSelected)
      ).then((totalAmountOut) => {
        if (totalAmountOut) {
          tokenAmountEstimateRef.current = multipliedBN(
            totalAmountOut,
            targetConvertPercent.current
          );
          setTokenAmountEstimate(tokenAmountEstimateRef.current);
        }
      });
    }, 1500),
    [activeWalletAddresses?.length, tokenQuoteSelected]
  );

  useEffect(() => {
    tokenAmountEstimateRef.current = "";
    setTokenAmountEstimate(tokenAmountEstimateRef.current);
    if (!accessToken || !amount) return;
    debouncedEstimateQuickBuy(pair, amount);

    return () => {
      debouncedEstimateQuickBuy.cancel();
    };
  }, [
    accessToken,
    amount,
    targetPrice,
    orderType,
    targetMC,
    activeWalletAddresses?.length,
    tokenQuoteSelected,
  ]);

  useEffect(() => {
    targetConvertPercent.current = "1";

    if (!accessToken || orderType !== OrderFormType.LIMIT) return;
    let rate;
    if (targetType === OrderLimitTargetType.MC) {
      rate = dividedBN(
        multipliedBN(pairPrice?.priceUsd, getCirculatingSupply(pair)),
        targetMC
      );
    } else if (targetType === OrderLimitTargetType.PRICE) {
      rate = dividedBN(pairPrice?.priceUsd, targetPrice);
    } else {
      return;
    }

    targetConvertPercent.current = rate;
    if (tokenAmountEstimateRef.current) {
      tokenAmountEstimateRef.current = multipliedBN(
        tokenAmountEstimateRef.current,
        rate
      );
      setTokenAmountEstimate(tokenAmountEstimateRef.current);
    }
  }, [accessToken, targetType, orderType, targetPrice, targetMC]);

  useEffect(() => {
    tokenAmountEstimateRef.current = "";
    setTokenAmountEstimate(tokenAmountEstimateRef.current);
    if (!amount) {
      setPercent("0");
      return;
    }

    if (!!+amount) {
      const newPercent = new BigNumber(
        dividedBN(amount, activeTotalQuoteBalance)
      )
        .multipliedBy(100)
        .integerValue(BigNumber.ROUND_FLOOR)
        .toString();
      setPercent(BigNumber.min(newPercent, 100).toString());
    }
  }, [accessToken, amount]);

  const _renderButton = () => {
    if (!accessToken) {
      return (
        <ButtonSignIn type={TRADE_TYPE.BUY} className="tablet:mt-2 !mt-0" />
      );
    }

    if (!wallets.length) {
      return (
        <ButtonAddWallet type={TRADE_TYPE.BUY} className="tablet:mt-2 !mt-0" />
      );
    }

    if (+amount > +activeTotalQuoteBalance || !+activeTotalQuoteBalance) {
      return (
        <>
          <ButtonAddFund
            className="tablet:mt-2 !mt-0"
            pair={pair}
            tokenQuoteSelected={tokenQuoteSelected}
          />
          {!isPairWithSui(pair) && !amount && (
            <div className="body-xs-regular-10 mt-2 text-center text-yellow-500">
              {`You need ${tokenQuoteSelected} to buy ${pair?.tokenBase?.symbol}`}
            </div>
          )}
        </>
      );
    }

    if (
      isDexHasBondingCurve(pair?.dex?.dex) &&
      Number(pair?.bondingCurve || 0) >= 1
    ) {
      return (
        <AppButton size="large" disabled variant="buy">
          {pair?.graduatedSlug
            ? `Migrated to ${getDexToWhenAfterGraduated(pair?.dex?.dex)}`
            : `Migrating to ${getDexToWhenAfterGraduated(pair?.dex?.dex)}`}
        </AppButton>
      );
    }

    if (orderType === OrderFormType.DCA) {
      return (
        <div
          className="tablet:mt-2 action-sm-medium-14 border-white-150 bg-brand-800 mt-0 flex h-[40px] cursor-pointer flex-col items-center justify-center rounded-[8px] border px-2"
          onClick={createOrder}
        >
          <div className="text-brand-500 md:body-md-medium-14 body-sm-medium-12">
            Buy{" "}
            {!!+amount &&
              `${amount} ${
                isPairWithSui(pair)
                  ? pair?.tokenQuote?.symbol
                  : tokenQuoteSelected
              }`}
          </div>
          {amountTime && amountOrders && (
            <div className="body-xs-medium-10 text-white-500">
              every {amountTime}
              {resolution.toLowerCase()} over {amountOrders} orders
            </div>
          )}
        </div>
      );
    }

    return (
      <div
        className="tablet:mt-2 action-sm-medium-14 border-white-150 bg-brand-800 mt-0 flex h-[40px] cursor-pointer flex-col items-center justify-center rounded-[8px] border px-2"
        onClick={createOrder}
      >
        <div className="text-brand-500 md:body-md-medium-14 body-sm-medium-12">
          Buy{" "}
          {!!+amount &&
            `${amount} ${
              isPairWithSui(pair)
                ? pair?.tokenQuote?.symbol
                : tokenQuoteSelected
            }`}
        </div>
        {!!+tokenAmountEstimate && (
          <div className="body-xs-medium-10 text-white-500">
            (≈{formatNumber(tokenAmountEstimate, pair?.tokenBase?.decimals)}{" "}
            {pair?.tokenBase?.symbol})
          </div>
        )}
      </div>
    );
  };

  const getTipAmount = () => {
    if (orderType === OrderFormType.LIMIT) {
      return settings.limitBuyTipAmount || "--";
    }

    return settings.quickBuyTipAmount || "--";
  };

  const getSlippage = () => {
    if (orderType === OrderFormType.LIMIT) {
      return settings.limitBuySlippage || "--";
    }

    return settings.quickBuySlippage || "--";
  };

  const getGasPrice = () => {
    if (orderType === OrderFormType.LIMIT) {
      return settings.limitBuyGasPrice || "--";
    }

    return settings.quickBuyGasPrice || "--";
  };

  if (isMobile) {
    return (
      <>
        <div className="mt-6">
          {isExceedBalance() && (
            <div className="body-xs-regular-10 mt-2 text-center text-yellow-500">
              Exceeds wallet balance
            </div>
          )}
          <div
            className={`mb-3 flex items-center items-center gap-2 gap-2 ${
              orderType !== OrderFormType.DCA
                ? "justify-between"
                : "justify-end"
            }`}
          >
            {orderType !== OrderFormType.DCA && (
              <div className="text-neutral-alpha-500 body-sm-medium-12 flex items-center gap-1">
                <AppToggle value={autoSell} onChange={toggleSetAutoSell} />
                <div
                  className="flex cursor-pointer items-center gap-1"
                  onClick={onShowSettingAutoSell}
                >
                  Auto Sell
                  <ChevronDownIcon className="w-4 rotate-[-90deg]" />
                </div>
              </div>
            )}

            <div className="item flex gap-2">
              <div className="text-neutral-alpha-500 body-sm-medium-12 border-neutral-alpha-50 flex items-center justify-center gap-1 border-r pr-2">
                <GasIcon />
                <div>{getGasPrice()}</div>
              </div>

              <div className="text-neutral-alpha-500 body-sm-medium-12 border-neutral-alpha-50 flex items-center justify-center gap-1 border-r pr-2">
                <CoinTip />
                <div>{getTipAmount()}</div>
              </div>

              <div className="text-neutral-alpha-500 body-sm-medium-12 flex items-center justify-center gap-1">
                <SlippageIcon />
                <div>{getSlippage()}%</div>
              </div>
            </div>
          </div>

          <div className="flex w-full items-center gap-2">
            <div className="flex-1">{_renderButton()}</div>
            <div
              onClick={onShowSettings}
              className="border-neutral-alpha-500 tablet:p-0 text-neutral-alpha-500 body-sm-medium-12 hover:text-neutral-alpha-1000 flex h-max cursor-pointer gap-1 rounded-md border border-solid p-2"
            >
              <SettingsIcon className="h-5 w-5" />
            </div>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <div className="mb-3 mt-9">
        <div
          className={`flex items-center gap-2 ${
            orderType !== OrderFormType.DCA ? "justify-between" : "justify-end"
          }`}
        >
          {orderType !== OrderFormType.DCA && (
            <div className="text-neutral-alpha-500 body-sm-medium-12 flex items-center gap-1">
              <AppToggle value={autoSell} onChange={toggleSetAutoSell} />
              <div
                className="flex cursor-pointer items-center gap-1"
                onClick={onShowSettingAutoSell}
              >
                Auto Sell
                <ChevronDownIcon className="rotate-[-90deg] cursor-pointer" />
              </div>
            </div>
          )}

          <div className="flex items-center gap-2">
            <div className="text-neutral-alpha-500 body-sm-medium-12 border-neutral-alpha-50 flex items-center justify-center gap-1 border-r pr-2">
              <GasIcon />
              <div>{getGasPrice()}</div>
            </div>

            <div className="text-neutral-alpha-500 body-sm-medium-12 flex w-max gap-1 ">
              <CoinTip />
              <div>{getTipAmount()}</div>
            </div>

            <div className="text-neutral-alpha-500 body-sm-medium-12 border-neutral-alpha-50 flex w-max gap-1 border-x px-2">
              <SlippageIcon />
              <div>{getSlippage()}%</div>
            </div>

            <div onClick={onShowSettings}>
              <SettingsIcon className="text-white-500 h-5 w-5 cursor-pointer" />
            </div>
          </div>
        </div>
      </div>

      <div>{_renderButton()}</div>

      {isExceedBalance() && (
        <div className="body-xs-regular-10 tablet:block mt-2 hidden text-center text-yellow-500">
          Exceeds wallet balance
        </div>
      )}
    </>
  );
};
