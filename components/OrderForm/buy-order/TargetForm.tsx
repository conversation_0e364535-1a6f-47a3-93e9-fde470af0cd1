import { OrderLimitTargetType } from "@/enums";
import { NumericFormat } from "react-number-format";
import { isZero, toStringBN } from "@/utils/helper";
import * as React from "react";
import { TPair } from "@/types";

const OPTIONS_TARGET_TYPE = [
  {
    name: "MC",
    value: OrderLimitTargetType.MC,
  },
  {
    name: "Price",
    value: OrderLimitTargetType.PRICE,
  },
];

export const TargetForm = ({
  targetType,
  setTargetType,
  targetMC,
  setTargetMC,
  targetPrice,
  setTargetPrice,
  pair,
}: {
  targetType: any;
  setTargetType: (value: any) => void;
  targetMC: any;
  setTargetMC: (value: any) => void;
  targetPrice: any;
  setTargetPrice: (value: any) => void;
  pair: TPair;
}) => {
  const _renderInput = () => {
    if (targetType === OrderLimitTargetType.MC) {
      return (
        <NumericFormat
          key={`TargetPrice-${OrderLimitTargetType.MC}`}
          defaultValue={targetMC ?? ""}
          value={targetMC ?? ""}
          allowLeadingZeros
          allowNegative={false}
          thousandSeparator=","
          className="tablet:body-md-semibold-14 body-sm-semibold-12 tablet:text-right w-full bg-transparent text-left outline-none"
          decimalScale={pair?.tokenBase?.decimals}
          onValueChange={({ floatValue }) => {
            if (!isZero(floatValue)) {
              return setTargetMC(toStringBN(floatValue));
            }
          }}
        />
      );
    }

    return (
      <NumericFormat
        key={`TargetPrice-${OrderLimitTargetType.PRICE}`}
        defaultValue={targetPrice ?? ""}
        value={targetPrice ?? ""}
        allowLeadingZeros
        allowNegative={false}
        thousandSeparator=","
        className="tablet:body-md-semibold-14 body-sm-semibold-12 tablet:text-right w-full bg-transparent text-left outline-none"
        decimalScale={10}
        onValueChange={({ floatValue }) => {
          if (!isZero(floatValue)) {
            setTargetPrice(toStringBN(floatValue));
          }
        }}
      />
    );
  };

  return (
    <div className="bg-neutral-beta-800 tablet:p-2 tablet:flex-row flex flex-row-reverse items-center gap-2 px-2">
      <div className="bg-neutral-alpha-50 flex gap-1 rounded-[4px] p-[2px] ">
        {OPTIONS_TARGET_TYPE.map((item, index) => {
          return (
            <div
              key={index}
              className={`action-xs-medium-12 cursor-pointer rounded-[4px] px-1 py-[2px] ${
                targetType === item.value
                  ? "bg-neutral-alpha-50 text-neutral-alpha-1000"
                  : "text-neutral-alpha-400"
              }`}
              onClick={() => setTargetType(item.value)}
            >
              {item.name}
            </div>
          );
        })}
      </div>

      <div className="tablet:flex-row flex flex-1 flex-row-reverse items-center gap-1">
        {_renderInput()}
        <div className="text-neutral-alpha-500 body-sm-regular-12">$</div>
      </div>
    </div>
  );
};
