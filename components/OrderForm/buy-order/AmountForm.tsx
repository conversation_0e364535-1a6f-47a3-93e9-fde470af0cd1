import { NumericFormat } from "react-number-format";
import { AppAvatarTokenQuote, AppLogoNetwork } from "@/components";
import ReactSlider from "react-slider";
import BigNumber from "bignumber.js";
import { dividedBN, isZero } from "@/utils/helper";
import * as React from "react";
import { formatNumberWithCommas } from "@/utils/format";
import { ChevronDownIcon } from "@/assets/icons";
import { useEffect, useRef, useState } from "react";
import { useMediaQuery } from "react-responsive";
import Storage from "@/libs/storage";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { TPair } from "@/types";
import { useTradingWallet } from "@/hooks/useTradingWallet";
import cx from "classnames";
import { NETWORKS } from "@/utils/contants";
import { isPairWithSui } from "@/utils/pair";

export const SelectTokenQuote: React.FC<{
  options: {
    icon: any;
    value: string;
  }[];
  onSelect: (value: string) => void;
  value?: string;
}> = ({ options, onSelect, value }) => {
  const [selected, setSelected] = useState<any>(options[0]);
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [isClient, setIsClient] = useState(false);
  const contentRef = useRef<any>(null);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (!isClient) return;
    const optionSelected = options.find((item) => item.value === value);
    setSelected(optionSelected);
  }, [value, options, isClient]);

  const handleClickOutside = (event: Event) => {
    if (
      contentRef.current &&
      !contentRef.current.contains(event.target as Node)
    ) {
      setShowDropdown(false);
    }
  };

  useEffect(() => {
    if (!isClient) return;
    document.addEventListener("click", handleClickOutside, true);
    return () => {
      document.removeEventListener("click", handleClickOutside, true);
    };
  }, [isClient]);

  if (!isClient) return null;

  return (
    <div
      className="relative"
      ref={contentRef}
      onClick={() => setShowDropdown(true)}
    >
      <div
        className={cx(
          "flex flex-row items-center justify-between",
          "gap-2",
          "w-max cursor-pointer"
        )}
      >
        <div>{selected?.icon}</div>
        <ChevronDownIcon
          className={`h-[10px] w-[10px] duration-500 ${
            showDropdown ? "rotate-[-180deg]" : "rotate-0"
          }`}
        />
      </div>

      {showDropdown && (
        <div
          style={{
            boxShadow: "4px 4px 8px 0px var(--Black-500, rgba(8, 9, 12, 0.50))",
            background:
              "linear-gradient(0deg, rgba(255, 255, 255, 0.10) 0%, rgba(255, 255, 255, 0.10) 100%), #08090C",
          }}
          className={cx(
            "absolute right-0 z-[9999] mt-1 w-full min-w-max rounded-[4px]"
          )}
        >
          <div className="flex w-full flex-col gap-1 p-1">
            {options.map((option, index) => {
              const isActive = option.value === value;
              return (
                <div
                  key={index}
                  className={cx(
                    "body-sm-regular-12 flex items-center gap-1 p-1",
                    "cursor-pointer",
                    "rounded-[4px]",
                    "tablet:hover:bg-neutral-alpha-50 text-neutral-0",
                    isActive ? "text-white-1000 bg-neutral-alpha-50" : ""
                  )}
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelected(option);
                    onSelect(option.value);
                    setShowDropdown(false);
                  }}
                >
                  {option.icon} {option.value}
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export const AmountForm = ({
  amount,
  setAmount,
  percent,
  setPercent,
  pair,
  tokenQuoteSelected,
  setTokenQuoteSelected,
  hideSliderRange,
}: {
  tokenQuoteSelected: string;
  setTokenQuoteSelected: (value: string) => void;
  amount: any;
  setAmount: (value: any) => void;
  percent: any;
  setPercent: (value: any) => void;
  pair: TPair;
  hideSliderRange: boolean;
}) => {
  const [isClient, setIsClient] = useState(false);
  const { activeTotalQuoteBalance } = useTradingWallet(
    pair,
    tokenQuoteSelected
  );
  const amountRef = useRef<any>("0");
  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });
  const orderSettings = Storage.getOrderSettings();

  useEffect(() => {
    setIsClient(true);
  }, []);

  const handleFocusInputAmount = () => {
    if (!isClient) return;
    const formattedAmount = formatNumberWithCommas(amount);
    amountRef.current?.setSelectionRange(
      formattedAmount.length,
      formattedAmount.length
    );
  };

  const OPTIONS_TOKEN_QUOTE = [
    {
      value: "SUI",
      icon: (
        <AppLogoNetwork
          network={NETWORKS.SUI}
          className={`w-[${14}px] h-[${14}px]`}
          isBase
        />
      ),
    },
    {
      value: pair?.tokenQuote?.symbol,
      icon: <AppAvatarTokenQuote pair={pair} />,
    },
  ];

  if (!isClient) return null;

  return (
    <>
      <div className="tablet:border border-neutral-alpha-50 tablet:rounded-[8px] mt-3">
        <div className="bg-neutral-beta-800 tablet:rounded-t-[8px] tablet:border-0 border-white-50 flex items-center gap-2 rounded-[4px] border p-2 ">
          <div className="body-sm-regular-12 text-neutral-alpha-800">
            Amount
          </div>
          <NumericFormat
            getInputRef={amountRef}
            defaultValue={amount ?? ""}
            value={amount ?? ""}
            autoFocus={!isMobile}
            onFocus={handleFocusInputAmount}
            allowLeadingZeros
            allowNegative={false}
            thousandSeparator=","
            className="body-md-semibold-14 w-full bg-transparent text-right outline-none"
            decimalScale={pair?.tokenQuote?.decimals}
            onValueChange={({ floatValue }) => {
              if (!isZero(floatValue)) {
                setAmount(floatValue?.toString());
              } else {
                setAmount("");
              }
            }}
          />

          {isPairWithSui(pair) ? (
            <div className="text-neutral-alpha-500">
              <AppAvatarTokenQuote pair={pair} />
            </div>
          ) : (
            <SelectTokenQuote
              options={OPTIONS_TOKEN_QUOTE}
              onSelect={(value) => {
                setTokenQuoteSelected(value);
                Storage.setUserSettings("tokenQuoteSelected", value);
              }}
              value={tokenQuoteSelected}
            />
          )}
        </div>

        <div className="tablet:gap-0 tablet:mt-0 mt-[4px] grid h-[34px] grid-cols-4 gap-1">
          {orderSettings?.defaultBuyAmount?.map((item, index) => {
            return (
              <div
                onClick={() => setAmount(item)}
                key={index}
                className={`g tablet:rounded-[0px] tablet:border-0 border-neutral-alpha-100 hover:bg-neutral-alpha-50 flex cursor-pointer items-center justify-center rounded-[4px] border py-[4px] text-center ${
                  index + 1 === 4
                    ? ""
                    : "tablet:border-r border-neutral-alpha-50"
                } ${
                  +amount === item
                    ? "bg-neutral-alpha-50"
                    : "body-md-regular-14"
                }`}
              >
                {item}
              </div>
            );
          })}
        </div>
      </div>

      {!hideSliderRange && (
        <div className="mt-3 flex items-end gap-4">
          <div className="flex-1">
            <ReactSlider
              value={+percent}
              disabled={!activeTotalQuoteBalance}
              onChange={(value: number) => {
                if (!+activeTotalQuoteBalance) return;
                setAmount(
                  new BigNumber(dividedBN(value, 100))
                    .multipliedBy(activeTotalQuoteBalance)
                    .toString()
                );
              }}
              className="bg-neutral-alpha-50 flex h-[4px] w-full cursor-pointer items-center"
              renderThumb={(props: any, state: any) => {
                const { key, ...restProps } = props;
                return (
                  <div
                    key={key}
                    {...restProps}
                    className="bg-white-1000 h-[12px] w-[12px] rounded-full"
                  />
                );
              }}
            />
          </div>
        </div>
      )}
    </>
  );
};
