import { EDex } from "@/enums";
import {
  DEXES_CAN_BUY_WITH_SUAI,
  DEXES_CAN_BUY_WITH_USDC,
  PLATFORMS_CAN_BUY_WITH_SUAI,
  SnipeCoin,
  SnipeTargetPair,
  USDC_ADDRESS,
} from "./constant";
import { SUI_ADDRESS } from "./constant";
import { SUAI_ADDRESS } from "./constant";
import { DEXS } from "@/utils/dex";

export const getTokenNameFromTokenAddress = (tokenAddress: string) => {
  if (tokenAddress === SUI_ADDRESS) {
    return SnipeCoin.SUI;
  }
  if (tokenAddress === SUAI_ADDRESS) {
    return SnipeCoin.SUAI;
  }
  if (tokenAddress === USDC_ADDRESS) {
    return SnipeCoin.USDC;
  }
  return SnipeCoin.SUI;
};

export const optionsTargetPair = (dexSelected: string) => {
  const dexSelectedConvert = convertValueDexSelected(dexSelected);
  let defaultOptions = [
    {
      label: SnipeTargetPair.TokenSUI,
      value: SUI_ADDRESS,
    },
  ];
  if (DEXES_CAN_BUY_WITH_USDC.includes(dexSelectedConvert)) {
    defaultOptions.push({
      label: SnipeTargetPair.TokenUSDC,
      value: USDC_ADDRESS,
    });
  }

  const uniqueOptions = Array.from(
    new Map(defaultOptions.map((option) => [option.value, option])).values()
  );
  defaultOptions = uniqueOptions;
  return defaultOptions;
};

export const optionsTargetPairFunzone = (dexSelected: string) => {
  const dexSelectedConvert = convertValueDexSelected(dexSelected);
  let defaultOptions = [
    {
      label: SnipeTargetPair.TokenSUI,
      value: SUI_ADDRESS,
    },
  ];

  if (PLATFORMS_CAN_BUY_WITH_SUAI.includes(dexSelectedConvert)) {
    defaultOptions.push({
      label: SnipeTargetPair.TokenSUAI,
      value: SUAI_ADDRESS,
    });
  }

  const uniqueOptions = Array.from(
    new Map(defaultOptions.map((option) => [option.value, option])).values()
  );
  defaultOptions = uniqueOptions;
  return defaultOptions;
};

const convertValueDexSelected = (dexSelected: string) => {
  if (dexSelected === "Sui Ai") {
    return DEXS[EDex.SUIAIFUN].name;
  }
  if (dexSelected === "Turbos.fun") {
    return DEXS[EDex.TURBOSFINANCE].name;
  }
  if (dexSelected === "Turbos.finance") {
    return DEXS[EDex.TURBOSFINANCE].name;
  }
  return dexSelected;
};
