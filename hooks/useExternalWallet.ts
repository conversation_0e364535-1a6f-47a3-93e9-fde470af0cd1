import { useCallback } from "react";
import { useCurrentAccount } from "@mysten/dapp-kit";
import { TPair } from "@/types";
import DexesFactory from "../utils/simulates/dexes/DexesFactory";
import { Transaction } from "@mysten/sui/transactions";
import { getReferenceGasPrice } from "../utils/suiClient";
import { convertDecToMist } from "../utils/helper";
import { SUI_DECIMALS } from "../utils/contants";
import useProcessTx from "./useProcessTx";
import { toastError } from "../libs/toast";
import { EDex } from "../enums/dex.enum";

export const useExternalWallet = () => {
  const currentAccount = useCurrentAccount();
  const { processTx } = useProcessTx();

  const onBuyToken = useCallback(
    async (
      pair: TPair,
      buyAmount: string,
      setIsLoading: (isLoading: boolean) => void,
      onSuccess?: (digest?: string) => void
    ) => {
      try {
        if (!currentAccount?.address) {
          return;
        }

        const pool = {
          dex: pair?.dex.dex as any,
          objectId: pair?.poolId,
        };

        setIsLoading(true);
        let tx: Transaction;
        const instance = DexesFactory.getDexesInstance(pair?.dex?.dex as EDex);
        const gasBasePrice = await getReferenceGasPrice();
        const exactAmountIn = convertDecToMist(buyAmount, SUI_DECIMALS);
        console.log(pool.dex, "pool.dex");

        if (pool.dex === EDex.MOVEPUMP || pool.dex === EDex.MOONBAGS) {
          tx = await instance.buildBuyTransaction(
            currentAccount.address,
            exactAmountIn,
            pair?.tokenBase,
            pair?.tokenQuote,
            pool?.objectId,
            gasBasePrice
          );
        } else if (pool.dex === EDex.SUIAIFUN) {
          tx = await instance.extractBaseTokenOut(
            currentAccount.address,
            exactAmountIn,
            pair?.tokenQuote,
            pair?.tokenBase,
            pool.objectId,
            gasBasePrice,
            true // isBuyBySuiToken
          );
        } else if (pool.dex === EDex.STEAMM) {
          tx = await instance.extractBaseTokenOut(
            currentAccount.address,
            exactAmountIn,
            pair?.tokenQuote,
            pair?.tokenBase,
            pair.isXQuoteToken,
            pair.feeTier,
            pool.objectId,
            gasBasePrice
          );
        } else {
          tx = await instance.extractBaseTokenOut(
            currentAccount.address,
            exactAmountIn,
            pair?.tokenQuote,
            pair?.tokenBase,
            pool.objectId,
            gasBasePrice
          );
        }

        await processTx(tx, onSuccess);
      } catch (error: any) {
        setIsLoading(false);
        toastError("Error ", error?.message || "Something went wrong");
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  const onSellToken = useCallback(
    async (
      pair: TPair,
      sellPercent: string,
      setIsLoading: (isLoading: boolean) => void,
      onSuccess?: (digest?: string) => void
    ) => {
      try {
        if (!currentAccount?.address) {
          return;
        }

        const pool = {
          dex: pair.dex.dex as any,
          objectId: pair.poolId,
        };

        setIsLoading(true);
        const instance = DexesFactory.getDexesInstance(pair?.dex?.dex as EDex);
        const gasBasePrice = await getReferenceGasPrice();

        const tx = await instance.extractQuoteTokenOut(
          currentAccount.address,
          pair?.tokenBase,
          pair?.tokenQuote,
          sellPercent,
          pool.objectId,
          gasBasePrice
        );
        const result = await processTx(tx, onSuccess);
        console.log(result, "xxx");
      } catch (error: any) {
        setIsLoading(false);
        toastError("Error ", error?.message || "Something went wrong");
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  return {
    onBuyToken,
    onSellToken,
  };
};
