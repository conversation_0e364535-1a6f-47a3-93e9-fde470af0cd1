import type { NextConfig } from "next";
import with<PERSON><PERSON> from "next-pwa";

const nextConfig: NextConfig = {
  /* config options here */
  experimental: {
    turbo: {
      rules: {
        "*.svg": {
          loaders: [
            {
              loader: "@svgr/webpack",
              options: {
                svgo: true,
                svgoConfig: {
                  plugins: [
                    {
                      name: "preset-default",
                      params: {
                        overrides: {
                          removeViewBox: false, // Ensure the viewBox attribute is preserved
                        },
                      },
                    },
                  ],
                },
              },
            },
          ],
          as: "*.js",
        },
      },
    },
  },
  headers: async () => {
    return [
      {
        source: "/charting_library/:path*",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=2592000, immutable", // Cache for 1 month
          },
        ],
      },
      {
        source: "/datafeeds/:path*",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=2592000, immutable", // Cache for 1 month
          },
        ],
      },
      {
        source: "/fonts/:path*",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=604800, immutable", // Cache for 1 week
          },
        ],
      },
      {
        source: "/images/:path*",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=86400, immutable", // Cache for 1 day
          },
        ],
      },
    ];
  },
  webpack(config) {
    const fileLoaderRule = config.module.rules.find((rule: any) =>
      rule.test?.test?.(".svg")
    );

    config.module.rules.push(
      {
        ...fileLoaderRule,
        test: /\.svg$/i,
        resourceQuery: /url/,
      },

      {
        test: /\.svg$/i,
        issuer: fileLoaderRule.issuer,
        resourceQuery: { not: [...fileLoaderRule.resourceQuery.not, /url/] },
        use: ["@svgr/webpack"],
      }
    );
    return config;
  },
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "storage.googleapis.com",
        pathname: "/raidenx-prod/**",
      },
      {
        protocol: "https",
        hostname: "storage.googleapis.com",
        pathname: "/raidenx/**",
      },
    ],
    minimumCacheTTL: 86400, // 1 day (24 * 60 * 60 seconds)
  },
  rewrites: async () => {
    return [
      {
        source: "/static/js/:path*",
        destination:
          "http://raidenx.io.s3-website-ap-southeast-1.amazonaws.com/static/js/:path*",
      },
      {
        source: "/static/css/:path*",
        destination:
          "http://raidenx.io.s3-website-ap-southeast-1.amazonaws.com/static/css/:path*",
      },
      {
        source: "/static/media/:path*",
        destination:
          "http://raidenx.io.s3-website-ap-southeast-1.amazonaws.com/static/media/:path*",
      },
      {
        source: "/docs/:path*",
        destination: "https://docs.raidenx.io/docs/:path*",
      },
      {
        source: "/assets/:path*",
        destination: "https://docs.raidenx.io/assets/:path*",
      },
    ];
  },
};

export default withPWA({
  dest: "public",
  register: true,
  skipWaiting: true,
  disable: process.env.NODE_ENV === "development",
})(nextConfig);
