import { io } from "socket.io-client";
import config from "@/config";
import { AppBroadcast, BROADCAST_EVENTS } from "@/libs/broadcast";
import {
  TPairSocket,
  TPairsStatsSocket,
  TPairTransactionSocket,
  TPositionUpdatedSocket,
  TPrivateSubscribed,
  TTokenInfoAuditSocket,
  TTokenInfoSocialSocket,
  TTradeSucceededSocket,
} from "@/types";
import { toastWarning } from "./toast";

declare global {
  interface Window {
    sockets: {
      [ESocketType: string]: ReturnType<typeof io> | null;
    };
  }
}

if (typeof window !== "undefined") {
  window.sockets = {};
}

export const SOCKET_EVENTS = {
  PRIVATE_SUBSCRIBED: "PRIVATE_SUBSCRIBED",
  PAIR_CREATED: "PAIR_CREATED",
  PAIR_STATS_UPDATED: "PAIR_STATS_UPDATED",
  TOKEN_INFO_SOCIAL_UPDATED: "TOKEN_INFO_SOCIAL_UPDATED",
  TOKEN_INFO_AUDIT_UPDATED: "TOKEN_INFO_AUDIT_UPDATED",
  TRANSACTION_CREATED: "TRANSACTION_CREATED",
  POSITION_UPDATED: "POSITION_UPDATED",
  ORDER_UPDATED: "ORDER_UPDATED",
  WALLET_UPDATED: "WALLET_UPDATED",
  TRADE_SUCCEEDED: "TRADE_SUCCEEDED",
  BONDING_CURVE_UPDATED: "BONDING_CURVE_UPDATED",
};

export const SOCKETS_ROOMS = {
  ALL_PAIR: "SUI::ALL_PAIR",
  TRENDING: "SUI::TRENDING",
  PAIR_DETAIL: (pairId: string) => `SUI::PAIR::${pairId}`,
};

export const socketInstance = (key: string): ReturnType<typeof io> | null =>
  window.sockets[key];

const createInstanceSocket = (socketUrl: string, accessToken?: string) => {
  return io(socketUrl, {
    transports: ["websocket", "polling"],
    reconnectionDelayMax: 5000,
    autoConnect: true,
    reconnection: true,
    ...(accessToken && { query: { authorization: accessToken } }),
    auth: { offset: undefined },
  });
};

export const closeInstanceSocket = (
  socketInstance: ReturnType<typeof io> | null
) => {
  if (socketInstance) {
    socketInstance.removeAllListeners();
    socketInstance.close();
  }
};

export const getSocketInstance = (network: string) => {
  if (!network) return null;
  const socketKey = `${network}`?.toUpperCase();
  return socketInstance(socketKey);
};

export const createSocketInstance = (network: string, accessToken?: string) => {
  const socketKey = `${network}`?.toUpperCase();
  const socketUrl = config.endpoints.ws;
  window.sockets[socketKey] = createInstanceSocket(socketUrl, accessToken);
  window.sockets[socketKey]?.on("connect", () => {
    console.log(
      `Websocket public connection ${network} is connected with server ${socketUrl}`
    );
    AppBroadcast.dispatch(BROADCAST_EVENTS.SOCKET_CONNECTED, {});

    getSocketInstance(network)?.on(
      SOCKET_EVENTS.PRIVATE_SUBSCRIBED,
      (data: TPrivateSubscribed) => {
        if (data.status != "success") {
          AppBroadcast.dispatch(BROADCAST_EVENTS.LOGOUT, {});
          toastWarning("Session expired", "Please login again to continue");
          return;
        }
      }
    );
    getSocketInstance(network)?.on(
      SOCKET_EVENTS.PAIR_CREATED,
      (data: TPairSocket) => {
        AppBroadcast.dispatch(BROADCAST_EVENTS.PAIR_CREATED, data);
      }
    );
    getSocketInstance(network)?.on(
      SOCKET_EVENTS.PAIR_STATS_UPDATED,
      (data: TPairsStatsSocket) => {
        AppBroadcast.dispatch(BROADCAST_EVENTS.PAIR_STATS_UPDATED, data);
      }
    );
    getSocketInstance(network)?.on(
      SOCKET_EVENTS.TOKEN_INFO_SOCIAL_UPDATED,
      (data: TTokenInfoSocialSocket) => {
        AppBroadcast.dispatch(BROADCAST_EVENTS.TOKEN_INFO_SOCIAL_UPDATED, data);
      }
    );
    getSocketInstance(network)?.on(
      SOCKET_EVENTS.TOKEN_INFO_AUDIT_UPDATED,
      (data: TTokenInfoAuditSocket) => {
        AppBroadcast.dispatch(BROADCAST_EVENTS.TOKEN_INFO_AUDIT_UPDATED, data);
      }
    );
    getSocketInstance(network)?.on(
      SOCKET_EVENTS.BONDING_CURVE_UPDATED,
      (data: TPairTransactionSocket) => {
        AppBroadcast.dispatch(BROADCAST_EVENTS.BONDING_CURVE_UPDATED, data);
      }
    );

    getSocketInstance(network)?.on(
      SOCKET_EVENTS.TRANSACTION_CREATED,
      (data: TPairTransactionSocket) => {
        AppBroadcast.dispatch(BROADCAST_EVENTS.TRANSACTION_CREATED, data);
      }
    );
    getSocketInstance(network)?.on(
      SOCKET_EVENTS.POSITION_UPDATED,
      (data: TPositionUpdatedSocket) => {
        AppBroadcast.dispatch(BROADCAST_EVENTS.POSITION_UPDATED, data);
      }
    );
    getSocketInstance(network)?.on(SOCKET_EVENTS.ORDER_UPDATED, (data: any) => {
      AppBroadcast.dispatch(BROADCAST_EVENTS.ORDER_UPDATED, data);
    });
    getSocketInstance(network)?.on(
      SOCKET_EVENTS.WALLET_UPDATED,
      (data: any) => {
        AppBroadcast.dispatch(BROADCAST_EVENTS.WALLET_UPDATED, data);
      }
    );
    getSocketInstance(network)?.on(
      SOCKET_EVENTS.TRADE_SUCCEEDED,
      (data: TTradeSucceededSocket) => {
        AppBroadcast.dispatch(BROADCAST_EVENTS.TRADE_SUCCEEDED, data);
      }
    );
  });

  window.sockets[socketKey]?.on("disconnect", (reason, details) => {
    console.log(
      `Websocket public connection ${network} is disconnected`,
      reason,
      details
    );
    AppBroadcast.dispatch(BROADCAST_EVENTS.SOCKET_DISCONNECTED, {});
  });
};

export const closeSocketInstance = (network: string) => {
  if (!network) return;
  const socketKey = `${network}`?.toUpperCase();
  closeInstanceSocket(socketInstance(socketKey));
  window.sockets[socketKey] = null;
};

export const subscribeSocketRoom = (network: string, room: string) => {
  if (!network) {
    throw new Error("Network is required");
  }
  getSocketInstance(network)?.emit("SUBSCRIBE", {
    room: room,
  });
};

export const unsubscribeSocketRoom = (network: string, room: string) => {
  if (!network) {
    throw new Error("Network is required");
  }
  getSocketInstance(network)?.emit("UNSUBSCRIBE", {
    room: room,
  });
};
