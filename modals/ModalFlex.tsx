"use client";

import { useEffect, useCallback, useState, useRef } from "react";
import * as htmlToImage from "html-to-image";
import QRCodeStyling, {
  CornerSquareType,
  CornerDotType,
} from "qr-code-styling";
import React from "react";
import ReactModal from "react-modal";
import { useSelector } from "react-redux";
import {
  CheckboxIcon,
  CheckedIcon,
  CloseIcon,
  CopyIcon,
  Logo,
} from "@/assets/icons";
import {
  FlexBgDown,
  FlexBgDownResponsive,
  FlexBgUp,
  FlexBgUpResponsive,
  QrIcon,
} from "@/public/images";
import { AppNumber } from "@/components";
import { toastError, toastSuccess } from "@/libs/toast";
import { RootState } from "@/store";
import { dividedBN, getSymbolTokenNative, multipliedBN } from "@/utils/helper";
import CircleLogo from "@/assets/icons/CircleLogo.svg";
import config from "@/config";
import rf from "@/services/RequestFactory";
import { useMediaQuery } from "react-responsive";
import { TPair } from "@/types";
import { getCirculatingSupply } from "@/utils/pair";
import { formatUnixTimestampToUTC } from "@/utils/format";
import Image from "next/image";
import { NETWORKS } from "@/utils/contants";
interface IModalFlexProps {
  isOpen: boolean;
  onClose: () => void;
  data: {
    isPnlUp: boolean;
    token: string;
    pairId: string;
    volumeUsdBought: string;
    volumeUsdSold: string;
    baseAmountBought: string;
    baseAmountSold: string;
    currentPnLPercent: string;
    currentPnLValueUsd: string;
    totalInvested: string;
    totalSold: string;
    tokenAddress: string;
    isClosed?: boolean;
  };
}

export const ModalFlex = ({ isOpen, onClose, data }: IModalFlexProps) => {
  const suiPriceUsd = useSelector(
    (state: RootState) => state.metadata.suiPriceUsd
  );
  const referralMyCode = useSelector(
    (state: RootState) => state.user.referralMyCode
  );
  const isMobile = useMediaQuery({ maxWidth: 992 });

  const [hideAmount, setHideAmount] = useState<boolean>(false);
  const [entryMc, setEntryMc] = useState<number>(0);
  const [currentMc, setCurrentMc] = useState<number>(0);
  const containerRef = useRef<HTMLDivElement>(null);

  const customStyles = {
    content: {
      top: "0%",
      left: "0%",
      right: "auto",
      bottom: "auto",
      borderRadius: "0px",
      border: 0,
      padding: 0,
      background: "transparent",
      overflow: "inherit",
    },
    overlay: {
      background: "rgba(8, 9, 12, 0.70)",
      backdropFilter: "blur(7.5px)",
      zIndex: 999,
    },
  };

  const handleCopyImage = async () => {
    if (!containerRef.current) return;
    try {
      const blob = await htmlToImage.toBlob(containerRef.current);
      if (blob) {
        document.body.focus();
        await navigator.clipboard.write([
          new ClipboardItem({
            "image/png": blob,
          }),
        ]);
        toastSuccess("Success", "Image copied to clipboard");
      }
    } catch (error) {
      console.error(error);
      toastError("Error", "Failed to copy image to clipboard");
    }
  };

  const getQrFlexLink = () => {
    return `${config.link_telegram}?start=${referralMyCode}`;
  };

  const getFlexLink = () => {
    return `${config.appUrl}@${referralMyCode}`;
  };

  const getSuiPrice = useCallback(async () => {
    const pairData: TPair = await rf
      .getRequest("PairRequest")
      .getPair(NETWORKS.SUI, data.pairId);
    if (pairData) {
      const supply = getCirculatingSupply(pairData);
      const avgPrice =
        Number(data.volumeUsdBought || 0) / Number(data.baseAmountBought || 0);
      const avgRealizedPrice =
        Number(data.volumeUsdSold || 0) / Number(data.baseAmountSold || 0);

      setEntryMc(Number(multipliedBN(avgPrice, supply)));

      if (data.isClosed) {
        setCurrentMc(Number(multipliedBN(avgRealizedPrice, supply)));
      } else {
        setCurrentMc(Number(multipliedBN(pairData.tokenBase.priceUsd, supply)));
      }
    }
  }, [data.tokenAddress]);

  useEffect(() => {
    getSuiPrice();
  }, [getSuiPrice]);

  return (
    <ReactModal
      isOpen={isOpen}
      onRequestClose={onClose}
      style={customStyles}
      ariaHideApp={false}
      bodyOpenClassName="overflow-hidden"
    >
      <div className="flex h-[100vh] w-[100vw] items-start justify-center md:items-center">
        <div className="flex h-full w-full max-w-[1068px] flex-col items-center justify-center px-4 py-5 md:px-0 md:py-10">
          <div className="flex w-full justify-end">
            <button
              className="group inline-flex h-10 cursor-pointer items-center justify-center gap-2 rounded-lg px-4 py-2.5"
              onClick={onClose}
            >
              <CloseIcon className="text-white-500 group-hover:text-white-1000" />
              <div className="inline-flex flex-col items-start justify-center rounded-sm">
                <div className="leading-[18px]text-white-500 group-hover:text-white-1000 self-stretch text-center text-sm font-medium">
                  Close
                </div>
              </div>
            </button>
          </div>
          <div className="inline-flex h-auto w-full flex-1 flex-col items-center justify-center gap-4 rounded-2xl md:w-max md:max-w-[594px] md:gap-6">
            <div className="flex w-full flex-1 flex-col items-center justify-center gap-4 self-stretch">
              <div className="h-full max-h-[443px] w-full overflow-hidden rounded-2xl md:max-h-[644px] lg:max-h-[594px]">
                <div
                  ref={containerRef}
                  className={`flex aspect-square h-full w-full flex-col items-center justify-start self-stretch px-10 pb-3 pt-6 md:px-28 md:pb-4 md:pt-14 ${
                    hideAmount ? "gap-6" : "gap-3"
                  }`}
                  style={{
                    backgroundImage: `url(${
                      isMobile
                        ? data.isPnlUp
                          ? "/images/Flex-ver-3-min.png"
                          : "/images/Flex-ver-4-min.png"
                        : data.isPnlUp
                        ? "/images/Flex-ver-1-min.png"
                        : "/images/Flex-ver-2-min.png"
                    })`,
                    backgroundSize: "100% 100%",
                    backgroundPosition: "center",
                    backgroundRepeat: "no-repeat",
                  }}
                >
                  <Logo />

                  <div className="flex h-auto flex-1 flex-col items-start justify-start gap-4 self-stretch">
                    <div className="relative flex h-auto flex-col items-center justify-start gap-2 self-stretch backdrop-blur-[1px]">
                      <div
                        className={`pointer-events-none absolute left-1/2 top-1/2 h-[110px] w-[300px] -translate-x-1/2 -translate-y-1/2 rounded-full blur-[70px] ${
                          data.isPnlUp ? "bg-green-500/30" : "bg-red-500/30"
                        }`}
                      ></div>
                      <div className="flex h-auto flex-col items-center justify-start gap-2 self-stretch">
                        <div className="flex h-[24px] flex-col items-start justify-center self-stretch rounded-sm">
                          <div className="text-white-1000 self-stretch text-center text-base font-semibold leading-[24px] md:text-[24px]">
                            ${data.token?.toUpperCase()}
                          </div>
                        </div>
                        <div className="flex flex-col items-start justify-center rounded-sm shadow md:h-[72px]">
                          <div
                            className={`self-stretch text-center ${
                              data.isPnlUp ? "text-[#63E69A]" : "text-[#FF6161]"
                            } text-[48px] font-bold leading-[48px] md:text-[72px] md:leading-[72px]`}
                            style={{
                              textShadow: data.isPnlUp
                                ? "0px 0px 20px rgba(39, 217, 113, 0.3)"
                                : "0px 0px 20px rgba(255, 77, 95, 0.3)",
                            }}
                          >
                            {data.currentPnLPercent}
                          </div>
                        </div>
                      </div>
                      {!hideAmount && (
                        <div
                          className={`px-3 py-0.5 ${
                            data.isPnlUp
                              ? "border-[#35A7653D] bg-[#072212]"
                              : "border-[#A735353D] bg-[#2F0404]"
                          } inline-flex items-center justify-start gap-1 rounded border`}
                        >
                          <div className="inline-flex flex-col items-start justify-center rounded-sm">
                            <div className="text-white-1000 self-stretch text-xs font-normal leading-[18px]">
                              Current PnL
                            </div>
                          </div>
                          <div className="flex items-center justify-start gap-1 rounded">
                            <div className="inline-flex flex-col items-start justify-center rounded-sm">
                              <div
                                className={`self-stretch ${
                                  data.isPnlUp
                                    ? "text-green-500"
                                    : "text-red-500"
                                } text-base font-medium leading-normal`}
                              >
                                <AppNumber
                                  value={dividedBN(
                                    data.currentPnLValueUsd,
                                    suiPriceUsd
                                  )}
                                />
                              </div>
                            </div>
                            <div className="inline-flex flex-col items-start justify-center rounded-sm">
                              <div
                                className={`self-stretch ${
                                  data.isPnlUp
                                    ? "text-green-500"
                                    : "text-red-500"
                                } text-base font-medium leading-normal`}
                              >
                                {getSymbolTokenNative(NETWORKS.SUI)}
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                    <div className="bg-white-50 border-white-50 flex h-auto flex-col items-center justify-start self-stretch rounded-[10px] border backdrop-blur-[1px]">
                      {hideAmount ? (
                        <>
                          <div className="border-white-50 inline-flex items-center justify-between self-stretch border-b border-dashed px-4 py-2.5">
                            <div className="inline-flex flex-col items-start justify-center rounded-sm">
                              <div className="text-white-600 md:body-sm-regular-12 body-xs-regular-10">
                                Entry MC
                              </div>
                            </div>
                            <div className="inline-flex flex-col items-start justify-center rounded-sm">
                              <div className="text-white-1000 md:body-md-regular-14 body-sm-regular-12 self-stretch text-right">
                                <AppNumber value={entryMc} isForUSD />
                              </div>
                            </div>
                          </div>
                          <div className="border-white-50 inline-flex items-center justify-between self-stretch border-b border-dashed px-4 py-2.5">
                            <div className="inline-flex flex-col items-start justify-center rounded-sm">
                              <div className="text-white-600 md:body-sm-regular-12 body-xs-regular-10">
                                {!!data.isClosed ? "Realized MC" : "Current MC"}
                              </div>
                            </div>
                            <div className="inline-flex flex-col items-start justify-center rounded-sm">
                              <div className="text-white-1000 md:body-md-regular-14 body-sm-regular-12 self-stretch text-right">
                                <AppNumber value={currentMc} isForUSD />
                              </div>
                            </div>
                          </div>
                        </>
                      ) : (
                        <>
                          <div className="border-white-50 inline-flex items-center justify-between self-stretch border-b border-dashed px-4 py-2.5">
                            <div className="inline-flex flex-col items-start justify-center rounded-sm">
                              <div className="text-white-600 md:body-sm-regular-12 body-xs-regular-10">
                                Total Invested
                              </div>
                            </div>
                            <div className="inline-flex flex-col items-start justify-center rounded-sm">
                              <div className="text-white-1000 md:body-md-regular-14 body-sm-regular-12 self-stretch text-right">
                                {data.totalInvested}$
                              </div>
                            </div>
                          </div>
                          <div className="border-white-50 inline-flex items-center justify-between self-stretch border-b border-dashed px-4 py-2.5">
                            <div className="inline-flex flex-col items-start justify-center rounded-sm">
                              <div className="text-white-600 md:body-sm-regular-12 body-xs-regular-10">
                                Total Sold
                              </div>
                            </div>
                            <div className="inline-flex flex-col items-start justify-center rounded-sm">
                              <div className="text-white-1000 md:body-md-regular-14 body-sm-regular-12 self-stretch text-right">
                                {data.totalSold}$
                              </div>
                            </div>
                          </div>
                          <div className="border-white-50 inline-flex items-center justify-between self-stretch px-4 py-2.5">
                            <div className="inline-flex flex-col items-start justify-center rounded-sm">
                              <div className="text-white-1000 md:body-sm-regular-12 body-xs-regular-10">
                                {data.isPnlUp ? "Total Profit" : "Total Loss"}
                              </div>
                            </div>
                            <div className="inline-flex flex-col items-start justify-center rounded-sm">
                              <div
                                className={`self-stretch text-right ${
                                  data.isPnlUp
                                    ? "text-green-500"
                                    : "text-[#FA3C3C]"
                                } md:body-md-regular-14 body-sm-regular-12`}
                              >
                                {data.currentPnLValueUsd}$
                              </div>
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                    <div className="bg-white-50 border-white-50 inline-flex h-auto w-full items-center justify-start gap-3 rounded-[10px] border py-2 pl-2 pr-4 backdrop-blur-[1px]">
                      <QRCodeSVG value={getQrFlexLink()} />
                      <div className="inline-flex w-[calc(100%-136px)] flex-1 flex-col items-start justify-center rounded-sm">
                        <div className="text-white-600 self-stretch text-[8px] font-normal leading-[14px]">
                          REFERRAL CODE
                        </div>
                        <div className="w-full">
                          <div className="text-white-1000 line-clamp-2 self-stretch break-words text-left text-[12px] font-semibold leading-normal md:text-base">
                            {isMobile ? getFlexLink().slice(8) : getFlexLink()}
                          </div>
                        </div>
                        <div className="text-white-600 self-stretch text-[10px] font-normal leading-none">
                          Refer other and earn up to 40%
                        </div>
                      </div>
                      <div className="inline-flex w-[32px] flex-col items-center justify-center gap-1 md:w-[40px]">
                        <Image
                          src={"/images/qrIcon.png"}
                          alt="qr"
                          className="h-auto w-10"
                          width={40}
                          height={40}
                        />
                      </div>
                    </div>
                  </div>
                  <div className="text-white-600 self-stretch text-center text-[12px] font-medium leading-[18px]">
                    Time Stamp: {formatUnixTimestampToUTC(new Date().valueOf())}
                  </div>
                </div>
              </div>

              <button
                className="bg-white-50 hover:bg-white-100 inline-flex items-center justify-center gap-1 self-stretch rounded-lg px-4 py-2.5"
                onClick={handleCopyImage}
              >
                <div className="inline-flex flex-col items-start justify-center rounded-sm">
                  <div className="text-white-1000 self-stretch text-center text-sm font-medium leading-[18px]">
                    Copy image
                  </div>
                </div>
                <CopyIcon className="text-white-1000" />
              </button>
              <div className="flex items-center justify-start self-stretch rounded-sm">
                <div className="text-white-1000 flex items-center gap-2 text-[10px] font-medium leading-[14px] md:text-[14px] md:leading-[18px]">
                  {hideAmount ? (
                    <CheckedIcon
                      className={`h-4 w-4 cursor-pointer`}
                      onClick={() => setHideAmount((prev) => !prev)}
                    />
                  ) : (
                    <CheckboxIcon
                      className={`h-4 w-4 cursor-pointer`}
                      onClick={() => setHideAmount((prev) => !prev)}
                    />
                  )}{" "}
                  Hide amount
                </div>
                <div className="text-white-700 hidden flex-1 self-stretch text-right text-[12px] font-normal leading-[18px] md:block">
                  Share your wins on Twitter and spread the RaidenX hype.
                </div>
                <div className="text-white-700 block flex-1 self-stretch text-right text-[10px] font-normal leading-[16px] md:hidden">
                  Share on Twitter and spread the RaidenX hype.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ReactModal>
  );
};

export const QRCodeSVG = ({
  value,
  className = "md:w-[72px] md:h-[72px] w-[64px] h-[64px]",
}: {
  value: string;
  className?: string;
}) => {
  const [qrImage, setQrImage] = React.useState<string>("");

  React.useEffect(() => {
    const generateQR = async () => {
      const qrCode = new QRCodeStyling({
        width: 160,
        height: 160,
        type: "svg",
        image: CircleLogo.src,
        shape: "square",
        data: value,
        dotsOptions: {
          color: "#000000",
          type: "rounded",
          roundSize: true,
        },
        backgroundOptions: {
          round: 0.2,
          color: "#FFFFFF",
        },
        margin: 0,
        imageOptions: {
          hideBackgroundDots: true,
          crossOrigin: "anonymous",
          margin: 0,
          imageSize: 0.5,
          saveAsBlob: true,
        },
        cornersSquareOptions: {
          color: "#222222",
          type: "extra-rounded" as CornerSquareType,
          gradient: {
            type: "linear",
            rotation: 180,
            colorStops: [
              { offset: 0, color: "#000000" },
              { offset: 1, color: "#000000" },
            ],
          },
        },
        cornersDotOptions: {
          color: "#222222",
          type: "dot" as CornerDotType,
          gradient: {
            type: "linear",
            rotation: 180,
            colorStops: [
              { offset: 0, color: "#000000" },
              { offset: 1, color: "#000000" },
            ],
          },
        },
      });

      try {
        const blob = await qrCode.getRawData("svg");
        if (blob) {
          const url = URL.createObjectURL(blob as Blob);
          setQrImage(url);
        }
      } catch (error) {
        console.error("Error generating QR code:", error);
      }
    };

    generateQR();
  }, [value]);

  if (!qrImage) return <div className={className} />;

  return (
    <Image
      src={qrImage}
      alt="QR Code"
      className={className}
      width={160}
      height={160}
    />
  );
};
