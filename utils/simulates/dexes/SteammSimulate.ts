import BigNumber from "bignumber.js";
import BaseSimulate from "../BaseSimulate";
import { TCoinMetadata } from "@/types";
import { Transaction } from "@mysten/sui/transactions";
import {
  getSuiObject,
  suiClient,
  getOwnerCoinOnchain,
} from "@/utils/suiClient";
import { SUI_TOKEN_ADDRESS_FULL } from "@/utils/contants";
import { CoinStruct } from "@mysten/sui/client";

export const STEAMM_PACKAGE =
  "0x4dfaa01e9fcc00ab2cd4f4b4dbb46ec502dfaf882ad1d8aa26d973949df41c7c";
export const STEAMM_LENDING_MARKET_TYPE =
  "0xf95b06141ed4a174f239417323bde3f209b972f5930d8521ea38a52aff3a6ddf::suilend::MAIN_POOL";
export const STEAMM_LENDING_MARKET_ID =
  "0x84030d26d85eaa7035084a057f2f11f701b7e2e4eda87551becbc7c97505ece1";

export default class SteammSimulate extends BaseSimulate {
  /**
   * Helper method to create and configure a base transaction
   */
  private createBaseTransaction = (
    walletAddress: string,
    gasBasePrice: bigint
  ): Transaction => {
    const tx = new Transaction();
    tx.setGasBudget(10000000);
    tx.setSender(walletAddress);
    tx.setGasPrice(gasBasePrice);
    return tx;
  };

  /**
   * Helper method to extract pool type information
   */
  private extractPoolTypeInfo = async (poolId: string) => {
    const poolObject = await getSuiObject({
      id: poolId,
      options: {
        showType: true,
      },
    });
    const poolType = poolObject.data?.type || "";

    const typeMatch = poolType.match(/Pool<([^>]+)>/);
    if (!typeMatch) throw new Error("Invalid pool type");
    const typeArgs = typeMatch[1].split(", ");

    return {
      bcoinAType: typeArgs[0],
      bcoinBType: typeArgs[1],
      lpTokenType: typeArgs[3],
    };
  };

  /**
   * Helper method to determine coin types based on quote/base tokens and direction
   */
  private getCoinTypes = (
    coinQuote: TCoinMetadata,
    coinBase: TCoinMetadata,
    isXQuoteToken: boolean
  ) => {
    return {
      coinAType: isXQuoteToken ? coinQuote.address : coinBase.address,
      coinBType: isXQuoteToken ? coinBase.address : coinQuote.address,
    };
  };

  /**
   * Helper method to create Steamm move call with common arguments
   */
  private createSteammMoveCall = (
    tx: Transaction,
    target: string,
    poolId: string,
    feeTier: any,
    coinA: any,
    coinB: any,
    isXQuoteToken: boolean,
    typeInfo: { bcoinAType: string; bcoinBType: string; lpTokenType: string },
    coinTypes: { coinAType: string; coinBType: string }
  ) => {
    tx.moveCall({
      target,
      typeArguments: [
        STEAMM_LENDING_MARKET_TYPE,
        coinTypes.coinAType,
        coinTypes.coinBType,
        typeInfo.bcoinAType,
        typeInfo.bcoinBType,
        typeInfo.lpTokenType,
      ],
      arguments: [
        tx.object(poolId),
        tx.object(feeTier.bank_a_object_id),
        tx.object(feeTier.bank_b_object_id),
        tx.object(STEAMM_LENDING_MARKET_ID),
        coinA,
        coinB,
        tx.object("0x6"),
        tx.pure.u64("0"),
        tx.pure.bool(isXQuoteToken),
        tx.pure.string("-1"), // order id
      ],
    });
  };

  private buildBuyTransaction = async (
    walletAddress: string,
    exactAmountIn: BigNumber | string | number,
    poolId: string,
    coinQuote: TCoinMetadata,
    coinBase: TCoinMetadata,
    isXQuoteToken: boolean,
    feeTier: any,
    gasBasePrice: bigint
  ) => {
    const tx = this.createBaseTransaction(walletAddress, gasBasePrice);
    const typeInfo = await this.extractPoolTypeInfo(poolId);
    const coinTypes = this.getCoinTypes(coinQuote, coinBase, isXQuoteToken);

    let coinA, coinB;

    const isSuiQuote = coinQuote.address === SUI_TOKEN_ADDRESS_FULL;

    let quoteCoinId: string | undefined;
    if (!isSuiQuote) {
      const coins = await suiClient.getCoins({
        owner: walletAddress,
        coinType: coinQuote.address,
      });

      if (coins.data.length === 0) {
        throw new Error(`No ${coinQuote.address} coin found for dummy sender`);
      }

      quoteCoinId = coins.data[0].coinObjectId;
    }

    if (coinTypes.coinAType == coinQuote?.address) {
      if (isSuiQuote) {
        [coinA] = tx.splitCoins(tx.gas, [
          tx.pure.u64(exactAmountIn.toString()),
        ]);
      } else {
        // Split from provided coin for non-SUI quote token
        if (!quoteCoinId)
          throw new Error("quoteCoinId required for non-SUI quote tokens");
        [coinA] = tx.splitCoins(tx.object(quoteCoinId), [
          tx.pure.u64(exactAmountIn.toString()),
        ]);
      }
      // Create empty coin B (output)
      coinB = tx.moveCall({
        target: "0x2::coin::zero",
        typeArguments: [coinTypes.coinBType],
      });
    } else {
      coinA = tx.moveCall({
        target: "0x2::coin::zero",
        typeArguments: [coinTypes.coinAType],
      });
      if (isSuiQuote) {
        [coinB] = tx.splitCoins(tx.gas, [
          tx.pure.u64(exactAmountIn.toString()),
        ]);
      } else {
        if (!quoteCoinId)
          throw new Error("quoteCoinId required for non-SUI quote tokens");
        [coinB] = tx.splitCoins(tx.object(quoteCoinId), [
          tx.pure.u64(exactAmountIn.toString()),
        ]);
      }
    }

    this.createSteammMoveCall(
      tx,
      `${STEAMM_PACKAGE}::steamm_cpmm_router::buy_exact_in`,
      poolId,
      feeTier,
      coinA,
      coinB,
      isXQuoteToken,
      typeInfo,
      coinTypes
    );

    return tx;
  };

  private buildSellTransaction = async (
    walletAddress: string,
    exactAmountIn: BigNumber | string | number,
    poolId: string,
    coinQuote: TCoinMetadata,
    coinBase: TCoinMetadata,
    isXQuoteToken: boolean,
    feeTier: any,
    gasBasePrice: bigint,
    coinObjs: (CoinStruct & { owner: string })[]
  ) => {
    const tx = this.createBaseTransaction(walletAddress, gasBasePrice);

    // Merge coins if multiple coin objects exist
    if (coinObjs.length > 1) {
      tx.mergeCoins(
        coinObjs[0].coinObjectId,
        coinObjs.slice(1).map((coin) => coin.coinObjectId)
      );
    }

    const typeInfo = await this.extractPoolTypeInfo(poolId);
    const coinTypes = this.getCoinTypes(coinQuote, coinBase, isXQuoteToken);

    let coinA, coinB;

    // For sell transactions, we're selling the base token (coinBase) to get quote token (coinQuote)
    // So we need to determine which coin is the input (base token we're selling)
    if (coinTypes.coinAType == coinBase?.address) {
      // Split the exact amount from the merged coin object
      [coinA] = tx.splitCoins(tx.object(coinObjs[0].coinObjectId), [
        tx.pure.u64(exactAmountIn.toString()),
      ]);
      // Create empty coin B (output - quote token)
      coinB = tx.moveCall({
        target: "0x2::coin::zero",
        typeArguments: [coinTypes.coinBType],
      });
    } else {
      // Create empty coin A
      coinA = tx.moveCall({
        target: "0x2::coin::zero",
        typeArguments: [coinTypes.coinAType],
      });
      // Split the exact amount from the merged coin object
      [coinB] = tx.splitCoins(tx.object(coinObjs[0].coinObjectId), [
        tx.pure.u64(exactAmountIn.toString()),
      ]);
    }

    this.createSteammMoveCall(
      tx,
      `${STEAMM_PACKAGE}::steamm_cpmm_router::sell_exact_in`,
      poolId,
      feeTier,
      coinA,
      coinB,
      isXQuoteToken,
      typeInfo,
      coinTypes
    );

    return tx;
  };

  /**
   * Helper method to validate feeTier data
   */
  private validateFeeTier = (feeTier: any) => {
    if (!feeTier || !feeTier.bank_a_object_id || !feeTier.bank_b_object_id) {
      throw new Error(
        "Fee tier data with bank_a_object_id and bank_b_object_id is required"
      );
    }
  };

  /**
   * Helper method to get user coin objects and calculate sell amount
   */
  private prepareSellTransaction = async (
    walletAddress: string,
    tokenIn: TCoinMetadata,
    sellPercent: number
  ) => {
    // Get user's coin objects for the token they want to sell
    const [coinObjs, currentAmount] = await getOwnerCoinOnchain(
      walletAddress,
      tokenIn.address
    );

    if (currentAmount.isZero()) {
      throw new Error("Insufficient balance");
    }

    // Calculate the exact amount to sell based on the percentage
    const exactAmountIn = currentAmount
      .multipliedBy(sellPercent)
      .div(100)
      .integerValue(BigNumber.ROUND_FLOOR);

    return { coinObjs, exactAmountIn };
  };

  public extractBaseTokenOut = async (
    walletAddress: string,
    amountIn: string,
    coinQuote: TCoinMetadata,
    coinBase: TCoinMetadata,
    isXQuoteToken: boolean,
    feeTier: any,
    poolObjectId: string | undefined,
    gasBasePrice: bigint
  ) => {
    if (!poolObjectId) {
      throw new Error("Pool object id not found");
    }

    return this.buildBuyTransaction(
      walletAddress,
      amountIn,
      poolObjectId,
      coinQuote,
      coinBase,
      isXQuoteToken,
      feeTier,
      gasBasePrice
    );
  };

  public extractQuoteTokenOut = async (
    walletAddress: string,
    tokenIn: TCoinMetadata,
    tokenOut: TCoinMetadata,
    sellPercent: number,
    poolObjectId: string | undefined,
    gasBasePrice: bigint,
    feeTier: any
  ) => {
    if (!poolObjectId) {
      throw new Error("Pool object id not found");
    }

    this.validateFeeTier(feeTier);

    const { coinObjs, exactAmountIn } = await this.prepareSellTransaction(
      walletAddress,
      tokenIn,
      sellPercent
    );

    // Determine if this is an X->Y or Y->X swap based on token addresses
    // For sell transactions, tokenIn is the base token and tokenOut is the quote token
    const isXQuoteToken = tokenOut.address === SUI_TOKEN_ADDRESS_FULL;

    return this.buildSellTransaction(
      walletAddress,
      exactAmountIn,
      poolObjectId,
      tokenOut, // coinQuote (what we're getting)
      tokenIn, // coinBase (what we're selling)
      isXQuoteToken,
      feeTier,
      gasBasePrice,
      coinObjs
    );
  };
}
