import BaseSimulate from "../BaseSimulate";
import { CoinStruct } from "@mysten/sui/client";
import { Transaction } from "@mysten/sui/transactions";
import BigNumber from "bignumber.js";
import { TCoinMetadata, TPosition } from "@/types";
import { isZero } from "@/utils/helper";
import {
  getOwnerCoinOnchain,
  getReferenceGasPrice,
  getSuiClient,
  RETRY_MAX_TIMEOUT,
  RETRY_MAX_ATTEMPT,
  RETRY_MIN_TIMEOUT,
  suiClient,
} from "@/utils/suiClient";
import { normalizeStructTag, normalizeSuiAddress } from "@mysten/sui/utils";
import retry from "async-retry";
import { coinWithBalance } from "@mysten/sui/transactions";
import { multipliedBN } from "@/utils/helper";

interface MoveObjectId {
  id: string;
}

interface MoonbagsPoolContent {
  id: MoveObjectId;
  is_completed: boolean;
  real_sui_reserves: any;
  real_token_reserves: any;
  remain_token_reserves: any;
  virtual_sui_reserves: string;
  virtual_token_reserves: string;
}

const MOONBAGS_PACKAGE =
  "0xbcc75126e136c2e64bb43ebc70650c8d1b01ad95484dc97b8e279816bffbf390";
const MOONBAGS_MODULE = "moonbags_router";
const MOONBAGS_CONFIG_OBJECT_ID =
  "0x74aecf86067c6913960ba4925333aefd2b1f929cafca7e21fd55a8f244b70499";
const MOONBAGS_FEE = 0.005;
const MOONBAGS_TRADING_FEE = 0.01;

const CONTRACT_CONFIG =
  "0x74aecf86067c6913960ba4925333aefd2b1f929cafca7e21fd55a8f244b70499";
const CETUS_BURN_MANAGER =
  "0x1d94aa32518d0cb00f9de6ed60d450c9a2090761f326752ffad06b2e9404f845";
const CETUS_FACTORY_POOL =
  "0xf699e7f2276f5c9a75944b37a0c5b5d9ddfd2471bf6242483b03ab2887d198d0";
const CETUS_GLOBAL_CONFIG =
  "0xdaa46292632c3c4d8f31f23ea0f9b36a28ff3677e9684980e4438403a67a3d8f";
const SUI_METADATA =
  "0x9258181f5ceac8dbffb7030890243caed69a9599d2886d957a9cb7656af3bdb3";

export default class MoonbagsSimulate extends BaseSimulate {
  public buildBuyTransaction = async (
    walletAddress: string,
    exactAmountIn: BigNumber | string | number,
    tokenOut: TCoinMetadata,
    tokenIn: TCoinMetadata,
    poolObjectId: string,
    gasBasePrice: bigint
  ) => {
    const tx = new Transaction();
    tx.setGasBudget(10000000);
    tx.setSender(walletAddress);
    tx.setGasPrice(gasBasePrice);

    const [coin] = tx.splitCoins(tx.gas, [
      tx.pure.u64(multipliedBN(exactAmountIn, 1.01).toString()),
    ]);

    tx.moveCall({
      target: `${MOONBAGS_PACKAGE}::moonbags_router::buy_exact_in`,
      typeArguments: [tokenOut?.address],
      arguments: [
        tx.object(CONTRACT_CONFIG),
        coin,
        tx.pure.u64(exactAmountIn?.toString()),
        tx.pure.u64(0),
        tx.object(CETUS_BURN_MANAGER),
        tx.object(CETUS_FACTORY_POOL),
        tx.object(CETUS_GLOBAL_CONFIG),
        tx.object(SUI_METADATA),
        tx.object("0x6"), // Clock object
        tx.pure.string("abc"),
      ],
    });

    return tx;
  };

  private getAmountOutExpectOnChain = async (
    poolId: string,
    exactAmountIn: BigNumber
  ) => {
    let client = suiClient;
    return await retry(
      async () => {
        const poolObj = await client.getObject({
          id: poolId,
          options: {
            showContent: true,
          },
        });
        console.log(poolObj);
        const poolContent = poolObj.data!.content;

        if (!poolContent || poolContent.dataType !== "moveObject") {
          throw new Error("Invalid pool content");
        }

        const poolContentFields =
          poolContent.fields as unknown as MoonbagsPoolContent;

        const amountInWithFee = exactAmountIn
          .multipliedBy(1.0 - MOONBAGS_TRADING_FEE!)
          .integerValue(BigNumber.ROUND_FLOOR);

        const amountExpectDecimals = this.calculateUniV2ExactOut(
          amountInWithFee,
          BigNumber(poolContentFields.virtual_sui_reserves),
          BigNumber(poolContentFields.virtual_token_reserves),
          BigNumber(MOONBAGS_FEE!)
        );

        return amountExpectDecimals;
      },
      {
        retries: RETRY_MAX_ATTEMPT,
        minTimeout: RETRY_MIN_TIMEOUT,
        maxTimeout: RETRY_MAX_TIMEOUT,
        onRetry: (e, attempt) => {
          console.log(`simulateBuyExactOutMoonbags retry ${attempt}`, e);
          client = getSuiClient(attempt);
        },
      }
    );
  };

  private calculateUniV2ExactOut = (
    amountIn: BigNumber,
    reserveIn: BigNumber,
    reserveOut: BigNumber,
    fee: BigNumber
  ): BigNumber => {
    const amountInWithFee = amountIn
      .multipliedBy(BigNumber(1).minus(fee))
      .integerValue(BigNumber.ROUND_FLOOR);
    const numerator = amountInWithFee.multipliedBy(reserveOut);
    const denominator = reserveIn.plus(amountInWithFee);
    return numerator.dividedBy(denominator).integerValue(BigNumber.ROUND_FLOOR);
  };

  private buildSellTransaction = (
    walletAddress: string,
    exactAmountIn: BigNumber,
    tokenIn: TCoinMetadata,
    gasBasePrice: bigint,
    coinObjs: (CoinStruct & { owner: string })[]
  ) => {
    const tx = new Transaction();
    tx.setGasBudget(10000000);
    tx.setSender(walletAddress);
    tx.setGasPrice(gasBasePrice);

    const coinAmountInObject = coinWithBalance({
      balance: exactAmountIn.toNumber(),
      type: tokenIn.address,
      useGasCoin: false,
    });

    tx.moveCall({
      target: `${MOONBAGS_PACKAGE}::${MOONBAGS_MODULE}::sell_exact_in`, // package
      typeArguments: [tokenIn.address],
      arguments: [
        tx.object(MOONBAGS_CONFIG_OBJECT_ID), // configObject,
        coinAmountInObject, // tokenInObject
        tx.pure.u64(0), // amountOutMin
        tx.object("0x6"), // clock
        tx.pure.string("abc"), // orderId
      ],
    });

    return tx;
  };

  public extractBaseTokenOut = async (
    walletAddress: string,
    amountIn: string,
    tokenIn: TCoinMetadata,
    tokenOut: TCoinMetadata,
    poolObjectId: string | undefined
  ) => {
    if (!poolObjectId) {
      throw new Error("Pool object id not found");
    }
    const amountExpectDecimals = await this.getAmountOutExpectOnChain(
      poolObjectId,
      new BigNumber(amountIn)
    );

    const slippage = 0;

    const amountExpect = BigNumber(amountExpectDecimals)
      .multipliedBy(BigNumber(1.0).minus(slippage))
      .dividedBy(BigNumber(10).pow(tokenOut.decimals));

    return amountExpect;
  };

  public extractQuoteTokenOut = async (
    walletAddress: string,
    tokenIn: TCoinMetadata,
    tokenOut: TCoinMetadata,
    sellPercent: number,
    poolObjectId: string | undefined,
    gasBasePrice: bigint
  ) => {
    if (!poolObjectId) {
      throw new Error("Pool object id not found");
    }

    const [coinObjs, currentAmount] = await getOwnerCoinOnchain(
      walletAddress,
      tokenIn.address
    );

    if (currentAmount.isZero()) {
      throw new Error("Insufficient balance");
    }

    const exactAmountIn = currentAmount
      .multipliedBy(sellPercent)
      .div(100)
      .integerValue(BigNumber.ROUND_FLOOR);

    return this.buildSellTransaction(
      walletAddress,
      exactAmountIn,
      tokenIn,
      gasBasePrice,
      coinObjs
    );
  };

  public extractQuoteTokenOutPositionWithSponsor = async (
    position: TPosition,
    userAllCoins: (CoinStruct & { owner: string })[],
    gasBasePrice?: bigint
  ) => {
    const tokenCoinObjects = userAllCoins.filter(
      (coin) =>
        normalizeStructTag(coin.coinType) ===
          normalizeStructTag(position.token.address) &&
        normalizeSuiAddress(coin.owner) ===
          normalizeSuiAddress(position.walletName)
    );
    if (!tokenCoinObjects) {
      throw new Error("Token not found");
    }
    const tokenBalance = tokenCoinObjects.reduce(
      (prev, coin) => prev.plus(coin.balance),
      new BigNumber(0)
    );

    if (!gasBasePrice) {
      gasBasePrice = await getReferenceGasPrice();
    }

    if (isZero(tokenBalance)) {
      throw new Error("Insufficient balance");
    }

    const exactAmountIn = tokenBalance
      .multipliedBy(100)
      .div(100)
      .integerValue(BigNumber.ROUND_FLOOR);

    const tx = await this.buildSellTransaction(
      position.walletName,
      exactAmountIn,
      position.token,
      gasBasePrice,
      tokenCoinObjects
    );
    tx.setSender(position.walletName);
    return await this.buildSponsoredTransaction(tx);
  };
}
