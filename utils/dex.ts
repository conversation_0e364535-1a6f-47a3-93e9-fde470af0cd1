import BigNumber from "bignumber.js";
import { EDex } from "@/enums/dex.enum";
import { TDexMap } from "@/types/dex.type";
import { minusBN } from "./helper";

export const DEXS = {
  [EDex.CETUS]: {
    id: "cetus",
    name: "<PERSON><PERSON>",
    logoUrl: "/images/cetus.png",
    website: "https://app.cetus.zone/swap",
  },
  [EDex.BLUEMOVE]: {
    id: "bluemove",
    name: "BlueMove",
    logoUrl: "/images/bluemove.png",
    website: "https://sui.bluemove.net/inscription/swap",
  },
  [EDex.MOVEPUMP]: {
    id: "movepump",
    name: "Move Pump",
    logoUrl: "/images/movepump.png",
    website: "https://movepump.com/swap",
  },
  [EDex.FLOWX]: {
    id: "flowx",
    name: "flowx",
    logoUrl: "/images/flowx.png",
    website: "https://flowx.finance/",
  },
  [EDex.FLOWX_V3]: {
    id: "flowxv3",
    name: "FlowX V3",
    logoUrl: "/images/flowx.png",
    website: "https://flowx.finance/",
  },
  [EDex.TURBOSFINANCE]: {
    id: "turbosfinance",
    name: "Turbos Finance",
    logoUrl: "/images/turbos_finance.png",
    website: "https://turbos.finance/",
  },
  [EDex.TURBOSFUN]: {
    id: "turbosfun",
    name: "Turbos.fun",
    logoUrl: "/images/turbos_finance.png",
    website: "https://app.turbos.finance/fun/",
  },
  [EDex.SEVENKFUN]: {
    id: "sevenkfun",
    name: "7k.fun",
    logoUrl: "/images/sevenkfun.png",
    website: "https://7k.fun/",
  },
  [EDex.BLUEFIN]: {
    id: "bluefin",
    name: "Bluefin",
    logoUrl: "/images/bluefin-logo.png",
    website: "https://bluefin.io/",
  },
  [EDex.SUIAIFUN]: {
    id: "suiai",
    name: "Suiai",
    logoUrl: "/images/suiai-logo.png",
    website: "https://suiai.fun/",
  },
  [EDex.MOONBAGS]: {
    id: "moonbags",
    name: "Moonbags",
    logoUrl: "/images/moonbags.png",
    website: "https://moonbags.io/",
  },
} satisfies TDexMap;

export const DEXES_HAS_BONDING_CURVE = [
  EDex.MOVEPUMP,
  EDex.TURBOSFUN,
  EDex.UPTOS,
  EDex.SEVENKFUN,
  EDex.SUIAIFUN,
  EDex.MOONBAGS,
];

export const MOVE_PUMP_BONDING_CURVE_DENOMINATOR = 2_000_000_000_000.0;
export const MOVE_PUMP_INITIAL_RESERVE = 500_000_000_000.0;

export const UPTOS_BONDING_CURVE_DENOMINATOR = 120_000_000_000.0;
export const UPTOS_INITIAL_RESERVE = 30_000_000_000.0;

export const TURBOS_FUN_BONDING_CURVE_DENOMINATOR = 6_000_000_000_000.0;
export const TURBOS_FUN_INITIAL_RESERVE = 1_500_000_000_000.0;

export const SEVEN_K_FUN_BONDING_CURVE_DENOMINATOR = 800_000_000_000_000_000.0;
export const SEVEN_K_FUN_INITIAL_RESERVE = 1_066_666_666_666_666_666.0; // 66_666_666_666

export const getDexLogoUrl = (dexKey: keyof typeof DEXS): string => {
  return DEXS[dexKey]?.logoUrl || "";
};

export const getDexWebsite = (dexKey: keyof typeof DEXS): string => {
  return DEXS[dexKey]?.website || "";
};

export const getDexName = (dexKey: keyof typeof DEXS): string => {
  return DEXS[dexKey]?.name || "";
};

export const getBondingCurveProcessing = (
  reserveQuote: string,
  denominator: number,
  initialReverse: number
) => {
  const percent = new BigNumber(
    minusBN(reserveQuote, initialReverse)
  ).dividedBy(denominator);
  return new BigNumber(percent).comparedTo(0) < 0 ? 100 : percent;
};

export const getBondingCurveProcessingSevenKFunDex = (
  reserveBase: string,
  denominator: number,
  initialReverse: number
) => {
  const percent = new BigNumber(minusBN(initialReverse, reserveBase)).dividedBy(
    denominator
  );

  if (percent.comparedTo(0) < 0) return 0;

  return percent.comparedTo(100) > 0 ? 100 : percent;
};

export const isDexHasBondingCurve = (dex: string) => {
  return DEXES_HAS_BONDING_CURVE.includes(dex as EDex);
};

export const getDexToWhenAfterGraduated = (dex: string) => {
  switch (dex) {
    case EDex.MOVEPUMP: {
      return DEXS[EDex.BLUEMOVE].name;
    }
    case EDex.TURBOSFUN: {
      return DEXS[EDex.TURBOSFINANCE].name;
    }
    case EDex.SEVENKFUN: {
      return DEXS[EDex.FLOWX].name;
    }
    case EDex.MOONBAGS: {
      return "Dex";
    }
    default: {
      return "";
    }
  }
};

export const getBondingCurveProcessingByDex = (
  dex: string,
  reserveQuote: string,
  reserveBase: string
) => {
  switch (dex) {
    case EDex.MOVEPUMP: {
      return getBondingCurveProcessing(
        reserveQuote,
        MOVE_PUMP_BONDING_CURVE_DENOMINATOR,
        MOVE_PUMP_INITIAL_RESERVE
      );
    }
    case EDex.TURBOSFUN: {
      return getBondingCurveProcessing(
        reserveQuote,
        TURBOS_FUN_BONDING_CURVE_DENOMINATOR,
        TURBOS_FUN_INITIAL_RESERVE
      );
    }

    case EDex.SEVENKFUN: {
      return getBondingCurveProcessingSevenKFunDex(
        reserveBase,
        SEVEN_K_FUN_BONDING_CURVE_DENOMINATOR,
        SEVEN_K_FUN_INITIAL_RESERVE
      );
    }

    default: {
      return "0";
    }
  }
};
