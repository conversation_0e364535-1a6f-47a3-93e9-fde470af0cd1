import { combineReducers, configureStore } from "@reduxjs/toolkit";
import { useDispatch } from "react-redux";
import user from "./user.store";
import metadata from "./metadata.store";

export type AppDispatch = typeof store.dispatch;

const rootReducer = combineReducers({
  user,
  metadata,
});

const store = configureStore({
  reducer: rootReducer,
});

export type RootState = ReturnType<typeof store.getState>;

export { store };
