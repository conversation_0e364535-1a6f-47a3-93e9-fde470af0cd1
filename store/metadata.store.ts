import { normalizeStructTag } from "@mysten/sui/utils";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import BigNumber from "bignumber.js";
import rf from "@/services/RequestFactory";
import { TPair } from "@/types";
import { SUI_TOKEN_ADDRESS_FULL } from "@/utils/contants";
import Storage from "@/libs/storage";
import { trackTwitterEvent } from "@/utils/twitterPixel";

export type TDex = {
  dex: string;
  logoUrl: string;
  name: string;
  version: string;
  website: string;
};

export type TPrice = {
  tokenAddress: string;
  price: string;
  priceUsd: string;
};

export type TMetadataStoreState = {
  connectedSocket: boolean;
  dexes: TDex[];
  dexesSnipe: TDex[];
  platformSnipe: TDex[];
  quotePrices: {
    [key: string]: TPrice;
  };
  suiPriceUsd: string;
  trendingPairsMeme: TPair[];
  isShowModalConnectTelegram: boolean;
  isShowModalLogin: boolean;
  isShowModalEnterCode: boolean;
  isShowModalAddWallet: boolean;
  isShowModalGetStart: boolean;
  isShowSidebarMenu: boolean;
  isHideInstallApp: boolean;
};

const ALL_DEXES = [
  "moonbags",
  "cetus",
  "flowx",
  "flowxv3",
  "turbosfinance",
  "turbosfun",
  // "bluemove",
  // "movepump",
  // "sevenkfun",
  "bluefin",
  // "suiai",
];

const ALL_DEXES_SNIPE = [
  "cetus",
  "flowx",
  "flowxv3",
  "turbosfinance",
  "bluefin",
  // "bluemove",
];
const ALL_DEXES_PLATFORM = [
  "moonbags",
  // "movepump",
  "turbosfun",
  // "sevenkfun",
  // "suiai",
];

export const getDexes = createAsyncThunk(
  "metadata/getDexes",
  async ({ network }: { network: string }, thunkApi) => {
    const data = await rf.getRequest("DexRequest").getDexes(network);

    const orderedDexes = <any>[];
    const orderedDexesSnipe = <any>[];
    const orderedDexesPlatform = <any>[];

    // Reorder based on predefined order
    ALL_DEXES.forEach((dexName) => {
      const dex = data.find((d: { dex: string }) => d.dex === dexName);
      if (dex) {
        orderedDexes.push(dex);
      }
    });
    ALL_DEXES_SNIPE.forEach((dexName) => {
      const dex = data.find((d: { dex: string }) => d.dex === dexName);
      if (dex) {
        orderedDexesSnipe.push(dex);
      }
    });
    ALL_DEXES_PLATFORM.forEach((dexName) => {
      const dex = data.find((d: { dex: string }) => d.dex === dexName);
      if (dex) {
        orderedDexesPlatform.push(dex);
      }
    });

    thunkApi.dispatch(setDexes({ dexes: orderedDexes }));
    thunkApi.dispatch(setDexesSnipe({ dexesSnipe: orderedDexesSnipe }));
    thunkApi.dispatch(
      setPlatformSnipe({ platformSnipe: orderedDexesPlatform })
    );
  }
);

export const getSuiPrice = createAsyncThunk(
  "price/getSuiPrice",
  async (_, thunkApi) => {
    const coinData = await rf.getRequest("PriceRequest").getSuiPrice();
    thunkApi.dispatch(
      setSuiPrice({
        priceUsd: new BigNumber(coinData.priceUsd).toFixed(8),
      })
    );
  }
);

export const getTrendingPairsMeme = createAsyncThunk(
  "metadata/getTrendingPairs",
  async ({ network }: { network: string }, thunkApi) => {
    const data = await rf
      .getRequest("TrendingRequest")
      .getTrendingMeme(network);

    thunkApi.dispatch(setTrendingPairsMeme({ trendingPairs: data }));
  }
);

const initialState: TMetadataStoreState = {
  connectedSocket: false,
  dexes: [],
  dexesSnipe: [],
  platformSnipe: [],
  suiPriceUsd: "0",
  trendingPairsMeme: [],
  quotePrices: {},
  isShowModalConnectTelegram: false,
  isShowModalLogin: false,
  isShowModalAddWallet: false,
  isShowModalEnterCode: false,
  isShowModalGetStart: false,
  isShowSidebarMenu: false,
  isHideInstallApp: Storage.getIsHideInstallApp() || false,
};

const metadataSlice = createSlice({
  name: "metatdata",
  initialState,
  reducers: {
    setConnectedSocket: (state, action) => {
      const { status } = action.payload;
      state.connectedSocket = status;
    },
    setDexes: (state, action) => {
      const { dexes } = action.payload;
      state.dexes = dexes;
    },
    setDexesSnipe: (state, action) => {
      const { dexesSnipe } = action.payload;
      state.dexesSnipe = dexesSnipe;
    },
    setPlatformSnipe: (state, action) => {
      const { platformSnipe } = action.payload;
      state.platformSnipe = platformSnipe;
    },
    setSuiPrice: (state, action) => {
      const { priceUsd }: TPrice = action.payload;
      state.suiPriceUsd = priceUsd;
    },
    setQuotePrice: (state, action) => {
      const { tokenAddress, priceUsd, price }: TPrice = action.payload;
      state.quotePrices[tokenAddress] = {
        tokenAddress,
        price,
        priceUsd,
      };
      if (
        tokenAddress &&
        normalizeStructTag(tokenAddress) === SUI_TOKEN_ADDRESS_FULL
      ) {
        state.suiPriceUsd = priceUsd;
      }
    },
    setTrendingPairsMeme: (state, action) => {
      const { trendingPairs } = action.payload;
      state.trendingPairsMeme = trendingPairs;
    },
    setTrendingPairMeme: (state, action) => {
      const newPair = action.payload;
      const existingIndex = state.trendingPairsMeme.findIndex(
        (pair) => pair.pairId === newPair.pairId
      );

      if (existingIndex >= 0) {
        state.trendingPairsMeme[existingIndex] = {
          ...state.trendingPairsMeme[existingIndex],
          volumeUsd: newPair?.volumeUsd,
          liquidityUsd: newPair?.liquidityUsd,
          stats: {
            ...state.trendingPairsMeme[existingIndex]?.stats,
            percent: newPair.percent,
          },
        };
        return;
      }
    },
    setIsShowModalConnectTelegram: (state, action) => {
      const { isShow } = action.payload;
      state.isShowModalConnectTelegram = isShow;
    },
    setIsShowModalLogin: (state, action) => {
      const { isShow } = action.payload;
      if (isShow) {
        trackTwitterEvent("Login");
      }
      state.isShowModalLogin = isShow;
    },
    setIsShowModalEnterCode: (state, action) => {
      const { isShow } = action.payload;
      state.isShowModalEnterCode = isShow;
    },
    setIsShowModalAddWallet: (state, action) => {
      const { isShow } = action.payload;
      state.isShowModalAddWallet = isShow;
    },
    setIsShowModalGetStart: (state, action) => {
      const { isShow } = action.payload;
      state.isShowModalGetStart = isShow;
    },
    setIsShowSidebarMenu: (state, action) => {
      const { isShow } = action.payload;
      state.isShowSidebarMenu = isShow;
    },
    setIsHideInstallApp: (state, action) => {
      const { isShow } = action.payload;
      state.isHideInstallApp = isShow;
    },
  },
});

export const {
  setDexes,
  setDexesSnipe,
  setPlatformSnipe,
  setSuiPrice,
  setQuotePrice,
  setTrendingPairsMeme,
  setTrendingPairMeme,
  setIsShowModalConnectTelegram,
  setIsShowModalAddWallet,
  setIsShowModalEnterCode,
  setIsShowModalGetStart,
  setIsShowSidebarMenu,
  setIsHideInstallApp,
  setIsShowModalLogin,
  setConnectedSocket,
} = metadataSlice.actions;

export default metadataSlice.reducer;
