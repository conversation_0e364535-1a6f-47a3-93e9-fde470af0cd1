"use client";

import clsx from "clsx";
import * as React from "react";
import { ReactNode, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useMediaQuery } from "react-responsive";
import {
  AlignJustifyIcon,
  IPositionIcon,
  WalletIcon,
  WebsiteIcon,
} from "@/assets/icons";
import config from "@/config";
import { ROUTE_PATH } from "@/routes";
import { RootState, AppDispatch } from "@/store";
import {
  setIsShowSidebarMenu,
  setIsShowModalLogin,
} from "@/store/metadata.store";
import { setIsExternalWallet } from "@/store/user.store";
import { Header } from "./Header";
import { InstallApp } from "@/layouts/landing";
import { isIOSMobile } from "@/utils/helper";
import { useRouter, usePathname } from "next/navigation";
import {
  ModalConnectTelegram,
  ModalLogin,
  ModalGetStart,
  ModalRequiredAddWallet,
} from "@/modals";
import { ModalEnterCode } from "@/modals/ModalEnterCode";
import { useCurrentAccount } from "@mysten/dapp-kit";

export const BasePage = ({
  children,
  title,
}: {
  children: ReactNode;
  title?: string;
}) => {
  const isShowModalConnectTelegram = useSelector(
    (state: RootState) => state.metadata.isShowModalConnectTelegram
  );
  const isShowModalLogin = useSelector(
    (state: RootState) => state.metadata.isShowModalLogin
  );
  const isShowModalAddWallet = useSelector(
    (state: RootState) => state.metadata.isShowModalAddWallet
  );
  const isShowModalGetStart = useSelector(
    (state: RootState) => state.metadata.isShowModalGetStart
  );

  const isShowModalEnterCode = useSelector(
    (state: RootState) => state.metadata.isShowModalEnterCode
  );

  const network = useSelector((state: RootState) => state.user.network);
  const isShowSidebarMenu = useSelector(
    (state: RootState) => state.metadata.isShowSidebarMenu
  );
  const isHideInstallApp = useSelector(
    (state: RootState) => state.metadata.isHideInstallApp
  );
  const [isClient, setIsClient] = useState(false);

  const dispatch = useDispatch<AppDispatch>();
  const pathname = usePathname();
  const router = useRouter();
  const isMobileAndTablet = useMediaQuery({ query: "(max-width: 992px)" });
  const currentAccount = useCurrentAccount();

  useEffect(() => {
    if (!!currentAccount?.address) {
      dispatch(setIsShowModalLogin({ isShow: false }));
      dispatch(setIsExternalWallet(true));
    }
  }, [currentAccount?.address]);

  const NAVIGATE_BAR_MOBILE = React.useMemo(
    () => [
      {
        title: "Market",
        url: ROUTE_PATH.NEW_PAIRS,
        icon: <WebsiteIcon />,
        isActive:
          [
            ROUTE_PATH.NEW_PAIRS,
            ROUTE_PATH.TRENDING,
            ROUTE_PATH.MEME_ZONE,
          ].includes(pathname) && !isShowSidebarMenu,
      },
      {
        title: "Position",
        url: `${ROUTE_PATH.POSITIONS}`,
        icon: <IPositionIcon />,
        isActive: pathname === ROUTE_PATH.POSITIONS && !isShowSidebarMenu,
      },
      {
        title: "Wallets",
        url: ROUTE_PATH.WALLET_MANAGER,
        icon: <WalletIcon />,
        isActive: pathname === ROUTE_PATH.WALLET_MANAGER && !isShowSidebarMenu,
      },
      {
        title: "Menu",
        icon: <AlignJustifyIcon />,
        isActive: isShowSidebarMenu,
        menu: true,
      },
    ],
    [pathname, isShowSidebarMenu]
  );

  useEffect(() => {
    document.title = title || config.title;
  }, [pathname]);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const isWebChartPage = pathname?.includes("/webchart/");
  const isHomePage = pathname === "/";
  const isAuthorizePage = pathname?.includes("/authorize");
  const isMobileAppPage = pathname?.includes("/mobile-app");
  const isAboutUsPage = pathname?.includes("/about-us");
  // const isDeveloperPage = pathname?.includes('/developer');
  const isHideHeaderPairDetailPage =
    pathname?.includes(`/${network}/`) && isMobileAndTablet;
  const isShowHeaders =
    !isWebChartPage &&
    !isHideHeaderPairDetailPage &&
    !isHomePage &&
    !isMobileAppPage &&
    !isAboutUsPage;
  // !isDeveloperPage

  if (!isClient) return <></>;

  return (
    <div
      className={`bg-neutral-beta-900 relative ${
        !isMobileAndTablet ? "min-h-screen" : ""
      }`}
    >
      {!isHideInstallApp && !isMobileAppPage && !isWebChartPage && (
        <InstallApp />
      )}

      {isShowHeaders && !isAuthorizePage && <Header />}
      <div>{children}</div>

      {isShowHeaders && (
        <div className="tablet:hidden border-b-white-50 fixed bottom-0 left-0 right-0 z-[100] grid grid-cols-4 border-b">
          {NAVIGATE_BAR_MOBILE.map((item) => {
            return (
              <div
                key={item?.title}
                className={clsx(
                  "border-white-100 flex cursor-pointer flex-col items-center justify-center gap-[4px] border-t px-[8px] py-[6px] text-[10px] font-semibold",
                  isIOSMobile() ? "pb-[18px] pt-[6px]" : "py-[6px]",
                  item?.isActive
                    ? "text-white-0 border-white-500 bg-[#1f2027]"
                    : "text-white-500 border-white-50 bg-[#08080c]"
                )}
                onClick={() => {
                  if (item?.url) {
                    router.push(item?.url);
                    dispatch(setIsShowSidebarMenu({ isShow: false }));
                  } else {
                    dispatch(
                      setIsShowSidebarMenu({ isShow: !isShowSidebarMenu })
                    );
                  }
                }}
              >
                {item?.icon} {item?.title}
              </div>
            );
          })}
        </div>
      )}

      {isShowModalConnectTelegram && <ModalConnectTelegram />}
      {isShowModalLogin && <ModalLogin />}
      {isShowModalAddWallet && <ModalRequiredAddWallet />}
      {isShowModalGetStart && <ModalGetStart />}
      {isShowModalEnterCode && <ModalEnterCode />}
    </div>
  );
};
